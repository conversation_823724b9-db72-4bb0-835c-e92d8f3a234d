using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[CustomEditor(typeof(SetVertexColor))]
public class SetVertexColorEditor : Editor
{
    private SerializedProperty sectionColorsProperty;
    private SerializedProperty originalMaterialProperty;



    private void OnEnable()
    {
        // 获取序列化属性
        sectionColorsProperty = serializedObject.FindProperty("sectionColors");
        originalMaterialProperty = serializedObject.FindProperty("originalMaterial");
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        SetVertexColor setVertexColor = (SetVertexColor)target;

        EditorGUILayout.Space();

        // 绘制sectionColors数组（固定大小为9）
        EditorGUILayout.LabelField("分段颜色设置 (9个区间)", EditorStyles.boldLabel);

        // 确保数组大小为9
        if (sectionColorsProperty.arraySize != 9)
        {
            sectionColorsProperty.arraySize = 9;
        }

        // 绘制每个颜色字段
        for (int i = 0; i < 9; i++)
        {
            string baseLabel = "";
            float targetGrayValue = 0f;

            switch (i)
            {
                case 0:
                    baseLabel = "0.9附近 ";
                    targetGrayValue = 0.9f;
                    break;
                case 1:
                    baseLabel = "0.7222附近 ";
                    targetGrayValue = 0.7222f;
                    break;
                case 2:
                    baseLabel = "0.6333附近 ";
                    targetGrayValue = 0.6333f;
                    break;
                case 3:
                    baseLabel = "0.5444附近 ";
                    targetGrayValue = 0.5444f;
                    break;
                case 4:
                    baseLabel = "0.4556附近 ";
                    targetGrayValue = 0.4556f;
                    break;
                case 5:
                    baseLabel = "0.3667附近 ";
                    targetGrayValue = 0.3667f;
                    break;
                case 6:
                    baseLabel = "0.2778附近 ";
                    targetGrayValue = 0.2778f;
                    break;
                case 7:
                    baseLabel = "0.1889附近 ";
                    targetGrayValue = 0.1889f;
                    break;
                case 8:
                    baseLabel = "0.1附近 ";
                    targetGrayValue = 0.1f;
                    break;
            }

            // 检查这个区间是否有效（基于预设的有效区间）
            bool isEffective = IsGrayValueEffective(targetGrayValue);

            // 创建标签内容
            GUIContent labelContent = new GUIContent("区间 " + i + ": " + baseLabel);

            // 根据有效性设置文字颜色
            if (!isEffective)
            {
                // 保存原始颜色
                Color originalColor = GUI.contentColor;
                GUI.contentColor = Color.gray; // 使用灰色表示无效区间

                EditorGUILayout.PropertyField(
                    sectionColorsProperty.GetArrayElementAtIndex(i),
                    labelContent
                );

                // 恢复原始颜色
                GUI.contentColor = originalColor;
            }
            else
            {
                // 有效区间使用红色文字
                Color originalColor = GUI.contentColor;
                GUI.contentColor = Color.red;

                EditorGUILayout.PropertyField(
                    sectionColorsProperty.GetArrayElementAtIndex(i),
                    labelContent
                );

                // 恢复原始颜色
                GUI.contentColor = originalColor;
            }
        }

        // 添加说明
        EditorGUILayout.HelpBox("有效区间：0.9、0.7222、0.6333、0.5444、0.4556、0.3667、0.2778、0.1889、0.1附近（±0.01范围）\n注意：0附近 (0-0.02) 的区域为不生效区间，将保持白色。", MessageType.Info);

        // 绘制分隔线
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
        EditorGUILayout.Space();



        // 绘制材质球管理部分
        EditorGUILayout.LabelField("材质球管理", EditorStyles.boldLabel);

        SetVertexColor vertexColorComponent = (SetVertexColor)target;

        // 绘制原始材质球字段
        EditorGUI.BeginChangeCheck();
        EditorGUILayout.PropertyField(originalMaterialProperty, new GUIContent("原始材质球"));
        if (EditorGUI.EndChangeCheck())
        {
            serializedObject.ApplyModifiedProperties();
        }

        // 显示预览材质球状态
        if (vertexColorComponent.previewMaterial != null)
        {
            EditorGUILayout.LabelField("预览材质球: " + vertexColorComponent.previewMaterial.name);
        }
        else
        {
            EditorGUILayout.LabelField("预览材质球: 未创建");
        }

        EditorGUILayout.Space();

        // 材质球操作按钮
        EditorGUILayout.BeginHorizontal();

        // 创建预览材质球按钮
        if (GUILayout.Button("创建预览材质球", GUILayout.Height(30)))
        {
            vertexColorComponent.CreatePreviewMaterial();
        }

        // 删除预览材质球按钮
        GUI.enabled = vertexColorComponent.previewMaterial != null;
        if (GUILayout.Button("删除预览材质球", GUILayout.Height(30)))
        {
            vertexColorComponent.DeletePreviewMaterial();
        }
        GUI.enabled = true;

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space();

        // 添加说明
        EditorGUILayout.HelpBox("预览模式：创建预览材质球后，颜色变化将通过Shader实时显示。", MessageType.Info);

        serializedObject.ApplyModifiedProperties();
    }


}