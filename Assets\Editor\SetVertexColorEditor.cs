using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

[CustomEditor(typeof(SetVertexColor))]
public class SetVertexColorEditor : Editor
{
    private SerializedProperty sectionColorsProperty;
    private SerializedProperty originalMaterialProperty;

    // 缓存分析结果（RGB三通道）
    private HashSet<float> effectiveRValues = new HashSet<float>();
    private HashSet<float> effectiveGValues = new HashSet<float>();
    private HashSet<float> effectiveBValues = new HashSet<float>();
    private bool analysisValid = false;



    private void OnEnable()
    {
        // 获取序列化属性
        sectionColorsProperty = serializedObject.FindProperty("sectionColors");
        originalMaterialProperty = serializedObject.FindProperty("originalMaterial");
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        SetVertexColor setVertexColor = (SetVertexColor)target;

        // 分析当前模型的顶点色，确定有效区间
        AnalyzeModelVertexColors(setVertexColor);

        EditorGUILayout.Space();

        // 绘制sectionColors数组（固定大小为27）
        EditorGUILayout.LabelField("分段颜色设置 (RGB三通道，每通道9个区间)", EditorStyles.boldLabel);

        // 确保数组大小为27
        if (sectionColorsProperty.arraySize != 27)
        {
            sectionColorsProperty.arraySize = 27;
        }

        // 定义区间值
        float[] sectionValues = {
            0.9f, 0.7222f, 0.6333f, 0.5444f, 0.4556f,
            0.3667f, 0.2778f, 0.1889f, 0.1f
        };

        // 绘制RGB三个通道
        string[] channelNames = { "R通道", "G通道", "B通道" };
        string[] channelLabels = { "R", "G", "B" };

        for (int channel = 0; channel < 3; channel++)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField(channelNames[channel], EditorStyles.boldLabel);

            for (int i = 0; i < 9; i++)
            {
                int colorIndex = channel * 9 + i;
                float targetValue = sectionValues[i];

                // 检查这个区间是否有效
                bool isEffective = IsChannelValueEffective(channel, targetValue);

                // 创建标签内容
                string baseLabel = $"{sectionValues[i]:F4}附近";
                GUIContent labelContent = new GUIContent($"{channelLabels[channel]}{i}: {baseLabel}");

                // 根据有效性设置文字颜色
                if (!isEffective)
                {
                    // 保存原始颜色
                    Color originalColor = GUI.contentColor;
                    GUI.contentColor = Color.black; // 使用黑色表示无效区间

                    EditorGUILayout.PropertyField(
                        sectionColorsProperty.GetArrayElementAtIndex(colorIndex),
                        labelContent
                    );

                    // 恢复原始颜色
                    GUI.contentColor = originalColor;
                }
                else
                {
                    // 有效区间使用正常颜色
                    EditorGUILayout.PropertyField(
                        sectionColorsProperty.GetArrayElementAtIndex(colorIndex),
                        labelContent
                    );
                }
            }
        }

        // 添加说明
        EditorGUILayout.HelpBox("有效区间规则：\n• 每个通道的区间值：0.9、0.7222、0.6333、0.5444、0.4556、0.3667、0.2778、0.1889、0.1（±0.01范围）\n• 生效条件：只有当某个通道在区间内且其他两个通道为0时，该区间才生效\n• 例如：(0.1, 0, 0) → R8生效；(0.1, 0, 0.1) → 都不生效", MessageType.Info);

        // 绘制分隔线
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
        EditorGUILayout.Space();



        // 绘制材质球管理部分
        EditorGUILayout.LabelField("材质球管理", EditorStyles.boldLabel);

        SetVertexColor vertexColorComponent = (SetVertexColor)target;

        // 绘制原始材质球字段
        EditorGUI.BeginChangeCheck();
        EditorGUILayout.PropertyField(originalMaterialProperty, new GUIContent("原始材质球"));
        if (EditorGUI.EndChangeCheck())
        {
            serializedObject.ApplyModifiedProperties();
        }

        // 显示预览材质球状态
        if (vertexColorComponent.previewMaterial != null)
        {
            EditorGUILayout.LabelField("预览材质球: " + vertexColorComponent.previewMaterial.name);
        }
        else
        {
            EditorGUILayout.LabelField("预览材质球: 未创建");
        }

        EditorGUILayout.Space();

        // 材质球操作按钮
        EditorGUILayout.BeginHorizontal();

        // 创建预览材质球按钮
        if (GUILayout.Button("创建预览材质球", GUILayout.Height(30)))
        {
            vertexColorComponent.CreatePreviewMaterial();
        }

        // 删除预览材质球按钮
        GUI.enabled = vertexColorComponent.previewMaterial != null;
        if (GUILayout.Button("删除预览材质球", GUILayout.Height(30)))
        {
            vertexColorComponent.DeletePreviewMaterial();
        }
        GUI.enabled = true;

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space();

        // 添加说明
        EditorGUILayout.HelpBox("预览模式：创建预览材质球后，颜色变化将通过Shader实时显示。", MessageType.Info);

        serializedObject.ApplyModifiedProperties();
    }

    // 分析当前模型的顶点色，确定有效区间（RGB三通道）
    private void AnalyzeModelVertexColors(SetVertexColor component)
    {
        effectiveRValues.Clear();
        effectiveGValues.Clear();
        effectiveBValues.Clear();
        analysisValid = false;

        var meshFilter = component.GetComponent<MeshFilter>();
        if (meshFilter == null || meshFilter.sharedMesh == null)
        {
            return;
        }

        var mesh = meshFilter.sharedMesh;
        var colors = mesh.colors;

        if (colors == null || colors.Length == 0)
        {
            return;
        }

        // 定义9个区间的中心值
        float[] sectionCenterValues = {
            0.9f,      // 区间0
            0.7222f,   // 区间1
            0.6333f,   // 区间2
            0.5444f,   // 区间3
            0.4556f,   // 区间4
            0.3667f,   // 区间5
            0.2778f,   // 区间6
            0.1889f,   // 区间7
            0.1f       // 区间8
        };

        const float tolerance = 0.01f; // 允许±0.01的误差范围

        // 分析每个顶点色，检查RGB三个通道
        // 只有当某个通道在区间内且其他两个通道为0时，该区间才生效
        foreach (var color in colors)
        {
            const float zeroTolerance = 0.01f; // 判断是否为0的容差

            // 检查R通道（只有当G=0且B=0时才生效）
            if (Mathf.Abs(color.g) <= zeroTolerance && Mathf.Abs(color.b) <= zeroTolerance)
            {
                foreach (var centerValue in sectionCenterValues)
                {
                    if (Mathf.Abs(color.r - centerValue) <= tolerance)
                    {
                        effectiveRValues.Add(centerValue);
                        break;
                    }
                }
            }

            // 检查G通道（只有当R=0且B=0时才生效）
            if (Mathf.Abs(color.r) <= zeroTolerance && Mathf.Abs(color.b) <= zeroTolerance)
            {
                foreach (var centerValue in sectionCenterValues)
                {
                    if (Mathf.Abs(color.g - centerValue) <= tolerance)
                    {
                        effectiveGValues.Add(centerValue);
                        break;
                    }
                }
            }

            // 检查B通道（只有当R=0且G=0时才生效）
            if (Mathf.Abs(color.r) <= zeroTolerance && Mathf.Abs(color.g) <= zeroTolerance)
            {
                foreach (var centerValue in sectionCenterValues)
                {
                    if (Mathf.Abs(color.b - centerValue) <= tolerance)
                    {
                        effectiveBValues.Add(centerValue);
                        break;
                    }
                }
            }
        }

        analysisValid = true;

        // 输出调试信息
        if (effectiveRValues.Count > 0 || effectiveGValues.Count > 0 || effectiveBValues.Count > 0)
        {
            string rValuesStr = effectiveRValues.Count > 0 ? string.Join(", ", effectiveRValues.Select(v => v.ToString("F4"))) : "无";
            string gValuesStr = effectiveGValues.Count > 0 ? string.Join(", ", effectiveGValues.Select(v => v.ToString("F4"))) : "无";
            string bValuesStr = effectiveBValues.Count > 0 ? string.Join(", ", effectiveBValues.Select(v => v.ToString("F4"))) : "无";

            Debug.Log($"检测到有效的顶点色区间:\nR通道: {rValuesStr}\nG通道: {gValuesStr}\nB通道: {bValuesStr}");
        }
        else
        {
            Debug.Log("未检测到有效的顶点色区间");
        }
    }

    // 检查指定通道的值是否在有效区间内（基于实际模型的顶点色分析）
    private bool IsChannelValueEffective(int channel, float targetValue)
    {
        if (!analysisValid)
        {
            return false;
        }

        const float tolerance = 0.01f; // 允许±0.01的误差范围

        HashSet<float> effectiveValues = null;
        switch (channel)
        {
            case 0: // R通道
                effectiveValues = effectiveRValues;
                break;
            case 1: // G通道
                effectiveValues = effectiveGValues;
                break;
            case 2: // B通道
                effectiveValues = effectiveBValues;
                break;
            default:
                return false;
        }

        foreach (var effectiveValue in effectiveValues)
        {
            if (Mathf.Abs(effectiveValue - targetValue) <= tolerance)
            {
                return true;
            }
        }

        return false;
    }

}