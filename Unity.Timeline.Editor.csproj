﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>9.0</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{f1f80ce1-e189-76ad-779c-9bcd0b3b462d}</ProjectGuid>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Unity.Timeline.Editor</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\Unity.Timeline.Editor\</OutputPath>
    <DefineConstants>UNITY_2021_3_31;UNITY_2021_3;UNITY_2021;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_RUNTIME_PERMISSIONS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;PLATFORM_ANDROID;TEXTCORE_1_0_OR_NEWER;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;ENABLE_ETC_COMPRESSION;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;USC_UNITY_PIPELINE_LEGACY;CROSS_PLATFORM_INPUT;MOBILE_INPUT;TIMELINE_FRAMEACCURATE;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169,0649</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\Menus\MenuItemActionBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\Scopes\HorizontalScope.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_Breadcrumbs.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\Control.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Undo\ApplyDefaultUndoAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\Invoker.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\CustomEditors\CustomTimelineEditorCache.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Utils\EditModeRippleUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\MarkerAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Utils\EditModeMixUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Sequence\TrackZoom.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Sequence\RectangleSelect.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\State\SequenceState.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Items\ClipItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\TimelineSelection.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Undo\UndoExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\MarkerModifier.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\AddDelete\IAddDeleteItemMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_Duration.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Tooltip.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Sequence\SelectAndMoveItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\Scopes\LabelWidthScope.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Items\ItemsGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\TimelineUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Signals\SignalEmitterEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\State\ISequenceState.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\Modes\TimelineDisabledMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_Gui.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\TimelineTreeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\Menus\TimelineContextMenu.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Signals\TreeView\SignalReceiverTreeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Utils\EditModeGUIUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Sequence\Jog.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Shortcuts.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindowTimeControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_EditorCallbacks.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\Scopes\IndentLevelScope.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Trim\TrimItemModeRipple.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Utils\PlacementValidity.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\AnimationClipCurveCache.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\ItemGui\TimelineMarkerGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\BindingTreeViewDataSource.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Attributes\MenuEntryAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Attributes\ActiveInModeAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\TimeFieldDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\TimelinePreferences.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\EditModeInputHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\ClipInspector\ClipInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\TrackGui\TrackResizeHandle.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Move\IMoveItemMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindowAnalytics.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\AnimationClipActions.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\BuiltInCurvePresets.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\GroupTrackInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Recording\TimelineRecording.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Audio\AudioPlayableAssetEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\EditorClip.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Recording\TimelineRecording_Monobehaviour.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\Drawers\TrackDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\Snapping\SnapEngine.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_PlayableLookup.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\Scopes\GUIViewportScope.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_ActiveTimeline.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Attributes\TimelineShortcutAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\Drawers\Layers\ClipsLayer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\TimeIndicator.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\TimelineKeyboardNavigation.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Signals\SignalEmitterInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\TrackPropertyCurvesDataSource.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Trim\TrimItemModeReplace.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\MenuPriority.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\ClipCurveEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\TimelineAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\TrackActions.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\TimelineClipHandle.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Items\MarkerItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\AddDelete\AddDeleteItemModeMix.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Signals\Styles.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\TypeUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Audio\AudioPlayableAssetInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\Modes\TimeReferenceMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\BindingTreeViewDataSourceGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Move\MoveItemModeMix.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\CurvesOwner\ICurvesOwnerInspectorWrapper.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\TimelineHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\ActionContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\ControlPlayableUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Trim\ITrimItemMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\IInspectorChangeHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\Scopes\GUIMixedValueScope.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Move\MoveItemHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\ClipsActions.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\ItemGui\TimelineMarkerClusterGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\PlaybackScroller.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\Scopes\GUIGroupScope.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\FrameRateDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\AnimatedParameterUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\PickerUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_Manipulators.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\ClipInspector\ClipInspectorCurveEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\ViewModel\TimelineAssetViewModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Recording\TrackAssetRecordingExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\BindingSelector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Properties\AssemblyInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_HeaderGui.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\ClipInspector\ClipInspectorSelectionInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\DirectorStyles.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\AddDelete\AddDeleteItemModeRipple.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\Drawers\TrackItemsDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\IPropertyKeyDataSource.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\TrackGui\TimelineTrackErrorGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_PlayRange.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\AnimationTrackKeyDataSource.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\Drawers\AnimationTrackDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\Scopes\PropertyScope.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Sequence\MarkerHeaderTrackManipulator.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Localization\Localization.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_TimeArea.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\AnimatedParameterCache.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\FileUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\State\WindowState.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\Scopes\GUIColorOverride.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\ItemGui\TimelineClipGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\DirectorNamedColorInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\AnimationPlayableAssetInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\TrackAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\Range.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\ViewModel\ScriptableObjectViewPrefs.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\SequenceSelectorNameFormater.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Utils\EditModeReplaceUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\ManipulationsTimeline.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\Modes\TimelineAssetEditionMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Recording\TimelineRecordingContextualResponder.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Audio\AudioTrackInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Sequence\RectangleTool.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\Drawers\Layers\ItemsLayer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\Modes\TimelineActiveMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Cursors\TimelineCursors.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Signals\SignalUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\CurveDataSource.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\AnimationPlayableAssetEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineMarkerHeaderGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\ControlTrack\ControlPlayableAssetEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\TimelineAnimationUtilities.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\TimelineInspectorUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\CurveEditUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineNavigator.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\IMenuName.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\TimelineTreeViewGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Extensions\AnimatedParameterExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\StyleManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_TimeCursor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Analytics\TimelineAnalytics.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\TimelineDragging.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\CustomTrackDrawerAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Signals\SignalReceiverInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\CustomEditors\MarkerTrackEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\UnityEditorInternals.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\TrackResourceCache.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\EditorClipFactory.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\ManipulationsClips.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\Drawers\InfiniteTrackDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\TimelineActions.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\BreadcrumbDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\TimeFormat.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\State\SequencePath.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\ClipAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\AnimationTrackActions.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\Graphics.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Recording\TimelineRecording_PlayableAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\SpacePartitioner.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\TrackGui\InlineCurveEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\TimelineProjectSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Trackhead.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\CurveTreeViewNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\StyleNormalColorOverride.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Extensions\TrackExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\AnimationClipExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\ManipulationsTracks.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\DisplayNameHelper.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_PreviewPlayMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\Modes\TimelineMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\AnimatedPropertyUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\TimelineEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\IRowGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\AddDelete\AddDeleteItemModeReplace.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Recording\AnimationTrackRecorder.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\TrackGui\TimelineTrackBaseGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\Drawers\Layers\MarkersLayer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\Modes\TimelineInactiveMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Utils\EditModeUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\TimelineClipGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\TimelineAssetInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\PropertyCollector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Activation\ActivationTrackEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Sequence\EaseClip.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\ViewModel\TimelineWindowViewPrefs.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Activation\ActivationTrackInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Utils\ManipulatorsUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Extensions\AnimationTrackExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\WindowConstants.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\EditMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\Snapping\ISnappable.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\TimelineDataSource.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\CustomEditors\MarkerEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\AnimationOffsetMenu.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\ClipModifier.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Signals\SignalReceiverHeader.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Audio\AudioClipPropertiesDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\CustomEditors\ClipEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\OverlayDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineEditorWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\Snapping\IAttractable.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\ItemGui\ISelectable.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\HeaderSplitterManipulator.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\CurvesOwner\CurvesOwnerInspectorHelper.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\ItemGui\TimelineItemGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Signals\SignalAssetInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\SequenceContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Undo\UndoScope.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\CustomEditors\TrackEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_StateChange.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Attributes\ShortcutAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\MarkerInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Sequence\RectangleZoom.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\TimelineClipUnion.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_Selection.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Items\ItemsPerTrack.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Trim\TrimItemModeMix.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\DirectorNamedColor.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Signals\SignalEventDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\IMenuChecked.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\IAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\Drawers\ClipDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\TrackModifier.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Items\ITimelineItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Sequence\TrimClip.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Items\ItemsUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Signals\TreeView\SignalReceiverItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Move\MovingItems.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Move\MoveItemModeReplace.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\TrackAssetInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\MarkerActions.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\FrameRateDisplayUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\BasicAssetInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\TrackGui\TimelineGroupGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_Navigator.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Actions\ActionManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\Modes\TimelineReadOnlyMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\BindingUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\ObjectExtension.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\TimeAreaAutoPanner.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Signals\TreeView\SignalListFactory.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Playables\ControlPlayableInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\Manipulator.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Window\TimelineWindow_TrackGui.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\ObjectReferenceField.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\Clipboard.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\TimeReferenceUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\treeview\TrackGui\TimelineTrackGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\State\SequenceHierarchy.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Animation\CurvesProxy.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Signals\SignalManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Utilities\KeyTraverser.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\inspectors\AnimationTrackInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Manipulators\Move\MoveItemModeRipple.cs" />
    <None Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\Unity.Timeline.Editor.asmdef" />
    <None Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\StyleSheets\res\Timeline_LightSkin.txt" />
    <None Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\StyleSheets\Extensions\dark.uss" />
    <None Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\StyleSheets\res\Timeline_DarkSkin.txt" />
    <None Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\StyleSheets\Extensions\light.uss" />
    <None Include="Library\PackageCache\com.unity.timeline@1.6.5\Editor\StyleSheets\Extensions\common.uss" />
    <Reference Include="UnityEngine">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.NVIDIAModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.NVIDIAModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsNativeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PackageManagerUIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.PackageManagerUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIServiceModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIServiceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\AndroidPlayer\UnityEditor.Android.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\unityplastic.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.visualscripting@1.9.1\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.visualscripting@1.9.1\Editor\VisualScripting.Core\EditorAssetResources\Unity.VisualScripting.TextureAssets.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.visualscripting@1.9.1\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll</HintPath>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\log4netPlastic.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.visualscripting@1.9.1\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="ReportGeneratorMerged">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.testtools.codecoverage@1.2.4\lib\ReportGenerator\ReportGeneratorMerged.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.ext.nunit@1.0.6\net35\unity-custom\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Unity.Timeline.csproj">
      <Project>{fcc6bebf-fb22-7069-58a0-b5f120bd4e36}</Project>
      <Name>Unity.Timeline</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEditor.UI.csproj">
      <Project>{e2909ef1-2b33-1495-5d01-36a01c357a5b}</Project>
      <Name>UnityEditor.UI</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEngine.UI.csproj">
      <Project>{e4e2ec11-4adb-d642-3e1b-0466e488a1e6}</Project>
      <Name>UnityEngine.UI</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEngine.TestRunner.csproj">
      <Project>{12c5ea48-a159-1e07-25be-9c7d4364f064}</Project>
      <Name>UnityEngine.TestRunner</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEditor.TestRunner.csproj">
      <Project>{725a3699-1238-328c-daad-7c09bbd4b914}</Project>
      <Name>UnityEditor.TestRunner</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
