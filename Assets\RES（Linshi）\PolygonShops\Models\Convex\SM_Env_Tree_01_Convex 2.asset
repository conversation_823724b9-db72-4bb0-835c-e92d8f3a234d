%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5b71ad40e238046238f9b0c6f33c3791, type: 3}
  m_Name: SM_Env_Tree_01_Convex 2
  m_EditorClassIdentifier: 
  ConvexMeshes:
  - {fileID: 43570639652477188}
  - {fileID: 43119311047300080}
  - {fileID: 43989159922223068}
  HashOfSourceMeshes: 505fcf0c576d5f61bbfb607000000000
--- !u!43 &43119311047300080
Mesh:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Generated convex submesh 2
  serializedVersion: 8
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 84
    topology: 0
    firstVertex: 0
    vertexCount: 16
    localAABB:
      m_Center: {x: 0.11357069, y: 1.032387, z: -0.007875711}
      m_Extent: {x: 0.39699918, y: 1.1471109, z: 0.26471794}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexBuffer: 080002000f00020001000400040005000600030004000600010002000900040001000900050004000900090007000a00060005000a00000006000a00070000000a00050009000a00060000000b00030006000b00000007000b0007000c000d00080003000d0003000b000d000b0007000d000c0008000d00020008000e00090002000e00070009000e0008000c000e000c0007000e00040003000f00020004000f00030008000f00
  m_Skin: []
  m_VertexData:
    m_CurrentChannels: 1
    m_VertexCount: 16
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 192
    _typelessdata: b5b4023f392d0040200901bc8a1d91be740ef53f200901bc8a1d91be740ef53fb6a12c3eff6b213e60f4eabdb6a12c3e16d247be00d2d8bcdac23cbeff6b213ee001a63f65918bbe9cc07b3e00d2d8bcdac23cbeeb34d83ee57c0b40b6a12c3ef2fadabd00d2d8bcd380833e8a1d91bee57c0b40d6c5c4bd9c0aab3ee57c0b40dac23cbe9cc07b3e00d2d8bcb6a12c3e82578e3d8092bc3fd380833eff6b213e00d2d8bcd380833e8a1d91bee57c0b40b6a12c3e16d247be00d2d8bcb6a12c3e
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0.11357069, y: 1.032387, z: -0.007875711}
    m_Extent: {x: 0.39699918, y: 1.1471109, z: 0.26471794}
  m_MeshUsageFlags: 0
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshOptimized: 0
--- !u!43 &43570639652477188
Mesh:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Generated convex submesh 1
  serializedVersion: 8
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 84
    topology: 0
    firstVertex: 0
    vertexCount: 16
    localAABB:
      m_Center: {x: -0.06747645, y: 4.5134926, z: -0.020025373}
      m_Extent: {x: 1.9016013, y: 0.92197526, z: 1.511933}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexBuffer: 0d0003000f00010002000500050002000600010005000700020001000800070004000800010007000800050006000a00090000000a00030008000b00080004000b00090003000b0009000b000c00040007000c00000009000c000b0004000c00060002000d00020008000d00080003000d00070005000e000a0000000e0005000a000e0000000c000e000c0007000e00030009000f000a0006000f0009000a000f0006000d000f00
  m_Skin: []
  m_VertexData:
    m_CurrentChannels: 1
    m_VertexCount: 16
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 192
    _typelessdata: a57638be51fb9b40495d9e3fafbc063f586b8540aeb7c0bfc078093fbfde8c403617c4bfbe0afcbfc6f39e40efc831beac2cbebe6bdb65407299dd3e9bc4ea3f222a8f40cb8009bfbabfc1bcc341ab40c9f224bf76e3c83ff8826b4010868bbe4f18d8be2ff475404eb690bf7c32acbf13dea1408d11a83fb90dc0bc5aefad40740d0b3f6c8befbf222a8f407c8a323fa0d822bf222a8f40d4f6be3fc302c8bf5b22a440fea43dbf8b8fbd3f5afc91401947833e99a9aebfd1bfad407f1f653e
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: -0.06747645, y: 4.5134926, z: -0.020025373}
    m_Extent: {x: 1.9016013, y: 0.92197526, z: 1.511933}
  m_MeshUsageFlags: 0
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshOptimized: 0
--- !u!43 &43989159922223068
Mesh:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Generated convex submesh 3
  serializedVersion: 8
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 84
    topology: 0
    firstVertex: 0
    vertexCount: 16
    localAABB:
      m_Center: {x: 0.20177114, y: 2.8854125, z: -0.051995367}
      m_Extent: {x: 0.74999523, y: 0.7059145, z: 0.39707687}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexBuffer: 090000000f00000003000400030001000400060003000700030000000700050006000700070000000900050007000900040001000a00080004000a00020001000b00010003000b00030006000b00010002000c000a0001000c0008000a000c00050009000d00090008000d000c0005000d0008000c000d00060005000e0002000b000e000b0006000e000c0002000e0005000c000e00000004000f00040008000f00080009000f00
  m_Skin: []
  m_VertexData:
    m_CurrentChannels: 1
    m_VertexCount: 16
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 192
    _typelessdata: 5a6febbe97ba2740ccb93cbef6a6733f4dd8654011eac4bd39065d3f73414f4048be01bc6a580cbf4dd8654086aeb03e1e41bebe4dd86540ccece5beffbc023fe57c0b4011eac4bdc24bd83ee57c0b4064962c3ee11291bee57c0b4064962c3e861dab3e73414f40ccece5bee11291be01261140ccb93cbef6a6733f4dd86540487f8bbee11291be4dd8654086aeb03e39065d3f2e4f3e40487f8bbeffbc023fbf132240487f8bbeffbc023f0126114064962c3e1e41bebe08e65440ccece5be
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0.20177114, y: 2.8854125, z: -0.051995367}
    m_Extent: {x: 0.74999523, y: 0.7059145, z: 0.39707687}
  m_MeshUsageFlags: 0
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshOptimized: 0
