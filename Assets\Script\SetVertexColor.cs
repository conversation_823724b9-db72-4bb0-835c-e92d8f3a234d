using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 顶点色设置组件
///
/// 工作流程：
/// 1. 预览模式：创建预览材质球，通过Shader实时显示颜色效果，不修改mesh数据
/// 2. 应用模式：点击"应用顶点色到Mesh"按钮，将颜色写入mesh实例并恢复原始材质球
///
/// 特性：
/// - 保护原始资源：原始mesh和材质球永不被修改
/// - 性能优化：预览模式避免mesh计算卡顿
/// - 数据安全：所有修改都在实例化副本中进行
/// </summary>
[ExecuteAlways]
public class SetVertexColor : MonoBehaviour
{
    // 用于存储9个不同颜色的数组，对应顶点R通道值的9个区间
    // 区间分别为：0.9、0.7222、0.6333、0.5444、0.4556、0.3667、0.2778、0.1889、0.1
    // 公开给外部工具访问，但在编辑器中锁定数组大小
    public Color[] sectionColors = new Color[9];

    // 原始的单一颜色设置（已弃用，保留向后兼容性）
    [System.Obsolete("单一颜色模式已弃用，现在默认使用分段颜色模式")]
    public Color vertexColor = Color.white;

    // 标记是否已经应用了颜色
    private bool colorApplied = false;

    // 缓存MeshFilter组件
    private MeshFilter meshFilter;

    // 静态共享的MaterialPropertyBlock，所有实例共用一个
    private static MaterialPropertyBlock sharedPropertyBlock;

    // 缓存Renderer组件
    private Renderer meshRenderer;

    // 颜色数组缓存
    private Color[] colorCache;



    // 材质球管理
    [Header("材质球设置")]
    [SerializeField]
    public Material originalMaterial;  // 原始材质球
    [SerializeField]
    public Material previewMaterial;   // 预览材质球
    [SerializeField]
    public string previewMaterialPath; // 预览材质球路径

    // 预览模式下的材质属性名称
    private static readonly string[] COLOR_PROPERTIES = {
        "_Color2", "_Color3", "_Color4", "_Color5", "_Color6",
        "_Color7", "_Color8", "_Color9", "_Color10"
    };

    // 预览shader路径
    private static readonly string PREVIEW_SHADER_NAME = "Custom/CityBuilding02_VertexColor";
    private static readonly string PREVIEW_MATERIAL_FOLDER = "Assets/Art/Environment/Preview";



#if UNITY_EDITOR
    void OnValidate()
    {
        // 确保在编辑器中修改属性时立即更新颜色，并应用颜色变化
        colorApplied = false;

        // 确保sectionColors数组大小为9
        if (sectionColors == null || sectionColors.Length != 9)
        {
            Color[] newColors = new Color[9];
            if (sectionColors != null)
            {
                for (int i = 0; i < Mathf.Min(sectionColors.Length, 9); i++)
                {
                    newColors[i] = sectionColors[i];
                }
            }
            sectionColors = newColors;
        }

        // 更新预览材质球的颜色
        UpdatePreviewMaterialColors();
    }
#endif

    // 在组件被重置时初始化颜色数组
    private void Reset()
    {
        // 初始化9个颜色，从亮到暗（白色到黑色）
        for (int i = 0; i < sectionColors.Length; i++)
        {
            // 计算亮度值，从1（白色）递减到0（黑色）
            float brightness = 1.0f - (float)i / (sectionColors.Length - 1);
            // 使用灰度色，亮度从高到低
            sectionColors[i] = new Color(brightness, brightness, brightness, 1.0f);
        }

        // 重置后不再自动应用颜色，只在用户点击应用按钮时才执行
    }

    private void Awake()
    {
        // 缓存组件引用
        meshFilter = GetComponent<MeshFilter>();
        meshRenderer = GetComponent<Renderer>();

        // 确保静态MaterialPropertyBlock已初始化
        if (sharedPropertyBlock == null)
        {
            sharedPropertyBlock = new MaterialPropertyBlock();
        }

#if UNITY_EDITOR
        // 保存原始材质球（如果尚未保存）
        if (originalMaterial == null && meshRenderer != null && meshRenderer.sharedMaterial != null)
        {
            originalMaterial = meshRenderer.sharedMaterial;
            Debug.Log("已保存原始材质球: " + originalMaterial.name);
        }
#endif

        // 确保sectionColors数组中的所有元素都有有效的颜色值
        InitializeColorsIfNeeded();

        // 初始化颜色缓存
        if (meshFilter != null && meshFilter.sharedMesh != null)
        {
            Mesh sharedMesh = meshFilter.sharedMesh;
            int vertexCount = sharedMesh.vertexCount;

            // 初始化颜色缓存
            colorCache = new Color[vertexCount];
        }
    }

    private void OnEnable()
    {
        // 确保颜色数组已初始化
        InitializeColorsIfNeeded();
    }









    // 公开方法，用于在运行时手动更新颜色（已弃用，请使用ApplyVertexColorToMesh）
    [System.Obsolete("请使用ApplyVertexColorToMesh方法")]
    public void UpdateColors()
    {
        // 不再自动应用颜色，只在用户点击应用按钮时才执行
        Debug.LogWarning("UpdateColors方法已弃用，请使用ApplyVertexColorToMesh方法");
    }





    // 确保颜色数组已初始化
    private void InitializeColorsIfNeeded()
    {
        // 强制确保sectionColors数组大小为9
        if (sectionColors == null)
        {
            sectionColors = new Color[9];
        }
        else if (sectionColors.Length != 9)
        {
            // 创建一个新的9元素数组
            Color[] newColors = new Color[9];

            // 复制现有颜色（如果有的话）
            for (int i = 0; i < Mathf.Min(sectionColors.Length, 9); i++)
            {
                newColors[i] = sectionColors[i];
            }

            // 替换原数组
            sectionColors = newColors;
        }

        // 检查是否需要初始化颜色值
        bool needsInitialization = false;

        // 检查每个颜色是否为默认值
        for (int i = 0; i < 9; i++)
        {
            // 如果颜色的RGBA都为0，认为是默认值
            if (sectionColors[i].r == 0 && sectionColors[i].g == 0 &&
                sectionColors[i].b == 0 && sectionColors[i].a == 0)
            {
                needsInitialization = true;
                break;
            }
        }

        // 如果需要初始化，设置默认颜色
        if (needsInitialization)
        {
            // 设置9个颜色，从亮到暗（白色到黑色）
            for (int i = 0; i < 9; i++)
            {
                // 计算亮度值，从1（白色）递减到0（黑色）
                float brightness = 1.0f - (float)i / 8.0f;
                // 使用灰度色，亮度从高到低
                sectionColors[i] = new Color(brightness, brightness, brightness, 1.0f);
            }
        }
    }

    // 获取指定索引的颜色
    public Color GetSectionColor(int index)
    {
        if (index >= 0 && index < 9)
        {
            return sectionColors[index];
        }
        return Color.white;
    }

    // 设置指定索引的颜色
    public void SetSectionColor(int index, Color color)
    {
        if (index >= 0 && index < 9)
        {
            sectionColors[index] = color;
            // 只更新预览材质球，不自动应用到mesh
#if UNITY_EDITOR
            UpdatePreviewMaterialColors();
#endif
        }
    }

    // 公开方法：更新预览材质球颜色（供外部工具调用）
    public void UpdatePreviewMaterialColorsPublic()
    {
#if UNITY_EDITOR
        UpdatePreviewMaterialColors();
#endif
    }

#if UNITY_EDITOR
    // 更新预览材质球的颜色
    private void UpdatePreviewMaterialColors()
    {
        if (previewMaterial != null)
        {
            // 将sectionColors数组的颜色设置到预览材质球中
            for (int i = 0; i < COLOR_PROPERTIES.Length && i < sectionColors.Length; i++)
            {
                previewMaterial.SetColor(COLOR_PROPERTIES[i], sectionColors[i]);
            }
        }
    }

    // 创建预览材质球
    public void CreatePreviewMaterial()
    {
        if (meshRenderer == null)
        {
            Debug.LogWarning("无法创建预览材质球：MeshRenderer组件不存在");
            return;
        }

        // 确保预览材质球文件夹存在
        if (!UnityEditor.AssetDatabase.IsValidFolder(PREVIEW_MATERIAL_FOLDER))
        {
            string[] folders = PREVIEW_MATERIAL_FOLDER.Split('/');
            string currentPath = folders[0];
            for (int i = 1; i < folders.Length; i++)
            {
                string newPath = currentPath + "/" + folders[i];
                if (!UnityEditor.AssetDatabase.IsValidFolder(newPath))
                {
                    UnityEditor.AssetDatabase.CreateFolder(currentPath, folders[i]);
                }
                currentPath = newPath;
            }
        }

        // 查找预览shader
        Shader previewShader = Shader.Find(PREVIEW_SHADER_NAME);
        if (previewShader == null)
        {
            Debug.LogError("无法找到预览shader: " + PREVIEW_SHADER_NAME);
            return;
        }

        // 生成唯一的材质球名称
        string baseName = gameObject.name + "_PreviewMaterial";
        string materialName = baseName;
        string materialPath = PREVIEW_MATERIAL_FOLDER + "/" + materialName + ".mat";
        int counter = 1;

        while (UnityEditor.AssetDatabase.LoadAssetAtPath<Material>(materialPath) != null)
        {
            materialName = baseName + "_" + counter;
            materialPath = PREVIEW_MATERIAL_FOLDER + "/" + materialName + ".mat";
            counter++;
        }

        // 创建新的材质球
        Material newMaterial = new Material(previewShader);
        newMaterial.name = materialName;

        // 如果有原始材质球，复制其属性
        if (originalMaterial != null)
        {
            // 复制贴图
            if (originalMaterial.HasProperty("_Diffuse") && newMaterial.HasProperty("_Diffuse"))
            {
                newMaterial.SetTexture("_Diffuse", originalMaterial.GetTexture("_Diffuse"));
            }

            // 复制其他通用属性
            if (originalMaterial.HasProperty("_Color") && newMaterial.HasProperty("_Color"))
            {
                newMaterial.SetColor("_Color", originalMaterial.GetColor("_Color"));
            }

            if (originalMaterial.HasProperty("_Cutout") && newMaterial.HasProperty("_Cutout"))
            {
                newMaterial.SetFloat("_Cutout", originalMaterial.GetFloat("_Cutout"));
            }
        }

        // 设置分段颜色
        for (int i = 0; i < COLOR_PROPERTIES.Length && i < sectionColors.Length; i++)
        {
            newMaterial.SetColor(COLOR_PROPERTIES[i], sectionColors[i]);
        }

        // 保存材质球到磁盘
        UnityEditor.AssetDatabase.CreateAsset(newMaterial, materialPath);
        UnityEditor.AssetDatabase.SaveAssets();

        // 更新引用
        previewMaterial = newMaterial;
        previewMaterialPath = materialPath;

        // 应用材质球到渲染器
        meshRenderer.sharedMaterial = previewMaterial;

        // 标记组件为已修改
        UnityEditor.EditorUtility.SetDirty(this);

        Debug.Log("已创建预览材质球: " + materialPath);
    }

    // 删除预览材质球
    public void DeletePreviewMaterial()
    {
        if (previewMaterial != null && !string.IsNullOrEmpty(previewMaterialPath))
        {
            // 恢复原始材质球
            if (originalMaterial != null && meshRenderer != null)
            {
                meshRenderer.sharedMaterial = originalMaterial;
            }

            // 删除预览材质球文件
            if (UnityEditor.AssetDatabase.LoadAssetAtPath<Material>(previewMaterialPath) != null)
            {
                UnityEditor.AssetDatabase.DeleteAsset(previewMaterialPath);
                UnityEditor.AssetDatabase.SaveAssets();
                Debug.Log("已删除预览材质球: " + previewMaterialPath);
            }

            // 清理引用
            previewMaterial = null;
            previewMaterialPath = "";

            // 标记组件为已修改
            UnityEditor.EditorUtility.SetDirty(this);
        }
        else
        {
            Debug.LogWarning("没有找到要删除的预览材质球");
        }
    }

    // 应用颜色的主方法
    private void ApplyColors()
    {
        // 如果已经应用过颜色且没有变化，则跳过
        if (colorApplied)
            return;

        // 只更新预览材质球参数
        if (previewMaterial != null)
        {
            UpdatePreviewMaterialColors();
            colorApplied = true;
            return;
        }

        // 标记颜色已应用
        colorApplied = true;
    }

#endif
}
