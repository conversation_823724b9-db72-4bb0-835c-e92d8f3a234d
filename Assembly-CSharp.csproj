﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>9.0</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{485e3fb3-b411-5e2a-5ded-b6f620d2d303}</ProjectGuid>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\Assembly-CSharp\</OutputPath>
    <DefineConstants>UNITY_2021_3_31;UNITY_2021_3;UNITY_2021;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_RUNTIME_PERMISSIONS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;PLATFORM_ANDROID;TEXTCORE_1_0_OR_NEWER;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;ENABLE_ETC_COMPRESSION;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;USC_UNITY_PIPELINE_LEGACY;CROSS_PLATFORM_INPUT;MOBILE_INPUT;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169,0649</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Assets\Scripts\Core\WaveData.cs" />
    <Compile Include="Assets\Scripts\Core\CameraShake.cs" />
    <Compile Include="Assets\Script\SceneColorCorrection.cs" />
    <Compile Include="Assets\Scripts\Core\DamageType.cs" />
    <Compile Include="Assets\Script\PlayerMovement.cs" />
    <Compile Include="Assets\Scripts\UI\UIManager.cs" />
    <Compile Include="Assets\Scripts\Player\ProjectileCollision.cs" />
    <Compile Include="Assets\Scripts\Gameplay\SceneScroller.cs" />
    <Compile Include="Assets\Scripts\Player\PlayerComponent.cs" />
    <Compile Include="Assets\Script\SetVertexColor.cs" />
    <Compile Include="Assets\Scripts\Gameplay\BackgroundMusic.cs" />
    <Compile Include="Assets\Scripts\Gameplay\GameManager.cs" />
    <Compile Include="Assets\Art\Projectiles\Demo scene\DemoShooting.cs" />
    <Compile Include="Assets\Scripts\ScenePrefabGenerator.cs" />
    <Compile Include="Assets\Script\SVNTool.cs" />
    <Compile Include="Assets\Scripts\Core\DamageInfo.cs" />
    <Compile Include="Assets\Scripts\Monster\MonsterComponent.cs" />
    <Compile Include="Assets\Scripts\Core\ProjectilePlaceholder.cs" />
    <Compile Include="Assets\Scripts\Gameplay\CombatSystem.cs" />
    <Compile Include="Assets\Scripts\Player\ProjectileMovement.cs" />
    <Compile Include="Assets\Scripts\Gameplay\ScrollWithScene.cs" />
    <Compile Include="Assets\Scripts\Gameplay\MonsterSystem.cs" />
    <Compile Include="Assets\Script\AnimatorControllerGenerator.cs" />
    <Compile Include="Assets\Scripts\PrefabCategoryData.cs" />
    <Compile Include="Assets\Art\Projectiles\Scripts\ProjectileMover.cs" />
    <Compile Include="Assets\Scripts\Gameplay\WaveDataSO.cs" />
    <Compile Include="Assets\Scripts\Core\IPoolableObject.cs" />
    <Compile Include="Assets\Scripts\Core\ObjectPoolManager.cs" />
    <Compile Include="Assets\Script\PrefabGenerator.cs" />
    <Compile Include="Assets\Script\CustomPPV.cs" />
    <Compile Include="Assets\Scripts\UI\DamageNumberBehaviour.cs" />
    <Compile Include="Assets\Art\Projectiles\Scripts\AutoDestroyPS.cs" />
    <Compile Include="Assets\RES（Linshi）\Standard Assets\Effects\ImageEffects\Scripts\ScreenOverlay.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\ScreenSpaceReflectionComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\FogComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Attributes\GetSetAttribute.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\ChromaticAberrationComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\City Adventure\Plugins\AnimationSystem\NodeScript.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\BloomModel.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\FxaaComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\GrainComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\MotionBlurModel.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\BuiltinDebugViewsComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\DepthOfFieldModel.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\VignetteModel.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Attributes\TrackballGroupAttribute.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Utils\ColorGradingCurve.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\GrainModel.cs" />
    <Compile Include="Assets\RES（Linshi）\Lowpoly Style\Shared Scripts\FlickerLight.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\EyeAdaptationComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\ChromaticAberrationModel.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\PostProcessingContext.cs" />
    <Compile Include="Assets\RES（Linshi）\Standard Assets\Effects\ImageEffects\Scripts\PostEffectsBase.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\ColorGradingModel.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Attributes\TrackballAttribute.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\AmbientOcclusionComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\MotionBlurComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\City Adventure\Plugins\AnimationSystem\NodeScriptUI.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\ScreenSpaceReflectionModel.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\PostProcessingModel.cs" />
    <Compile Include="Assets\RES（Linshi）\City Adventure\Plugins\ColorSuite\ColorSuite.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\DepthOfFieldComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Attributes\MinAttribute.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\UserLutComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\ColorGradingComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\PostProcessingBehaviour.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\EyeAdaptationModel.cs" />
    <Compile Include="Assets\RES（Linshi）\City Adventure\Plugins\DealyedActivation.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Utils\GraphicsUtils.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\AmbientOcclusionModel.cs" />
    <Compile Include="Assets\RES（Linshi）\City Adventure\Plugins\AnimationSystem\CarPath.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\AntialiasingModel.cs" />
    <Compile Include="Assets\RES（Linshi）\Lowpoly Style\Shared Scripts\UVOffset.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\DitheringComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Utils\RenderTextureFactory.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\UserLutModel.cs" />
    <Compile Include="Assets\RES（Linshi）\City Adventure\Plugins\CameraFollow.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\BuiltinDebugViewsModel.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\PostProcessingProfile.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\PostProcessingComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Utilities\CustomMotionTexture\ExampleWheelController.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\BloomComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\VignetteComponent.cs" />
    <Compile Include="Assets\RES（Linshi）\City Adventure\Plugins\AnimationSystem\CarPathUI.cs" />
    <Compile Include="Assets\RES（Linshi）\City Adventure\Plugins\AnimationSystem\NodeListClass.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Utils\MaterialFactory.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\FogModel.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Models\DitheringModel.cs" />
    <Compile Include="Assets\RES（Linshi）\PostProcessing\Runtime\Components\TaaComponent.cs" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\clientcommon.ja.txt" />
    <None Include="Library\PackageCache\com.unity.settings-manager@1.0.3\package.json" />
    <None Include="Library\PackageCache\com.unity.modules.subsystems@1.0.0\package.json" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro.cginc" />
    <None Include="Library\PackageCache\com.unity.modules.jsonserialize@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\i3.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\semantic.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.modules.screencapture@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.modules.cloth@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\workspaceserver.es.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Overlay.shader" />
    <None Include="Library\PackageCache\com.unity.modules.unitywebrequesttexture@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.modules.ai@1.0.0\package.json" />
    <None Include="Assets\Shader\Character02.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\basecommands.en.txt" />
    <None Include="Library\PackageCache\com.unity.ugui@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\xdiff.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\basecommands.es.txt" />
    <None Include="Library\PackageCache\com.unity.ext.nunit@1.0.6\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\mergetool.es.txt" />
    <None Include="Assets\Shader\CityBuilding02_VertexColor.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\cm.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\cm-help.ko.txt" />
    <None Include="Library\PackageCache\com.unity.modules.assetbundle@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\cm-help.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\basecommands.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\plastic-gui.ko.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap.shader" />
    <None Include="Library\PackageCache\com.unity.textmeshpro@3.0.9\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\cm-help.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\i3.ko.txt" />
    <None Include="Library\PackageCache\com.unity.modules.imgui@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\workspaceserver.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\differences.ko.txt" />
    <None Include="Library\PackageCache\com.unity.modules.tilemap@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\images.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\mergetool.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.timeline@1.6.5\package.json" />
    <None Include="Assets\Shader\Monster.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\xdiff.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\xdiff.en.txt" />
    <None Include="Assets\Art\Projectiles\Demo scene\Readme.txt" />
    <None Include="Library\PackageCache\com.unity.ide.visualstudio@2.0.21\ValidationConfig.json" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Mobile.cginc" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\cm.es.txt" />
    <None Include="Assets\Art\Projectiles\VFX_Klaus\Shaders\Explosion_lab.shader" />
    <None Include="Library\PackageCache\com.unity.modules.terrainphysics@1.0.0\package.json" />
    <None Include="Assets\Art\Projectiles\Shaders\DissolveNoise.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF SSD.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\workspaceserver.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\cm-help.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.visualscripting@1.9.1\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\guihelp.ko.txt" />
    <None Include="Library\PackageCache\com.unity.editorcoroutines@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.timeline@1.6.5\ValidationExceptions.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\configurehelper.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\commontypes.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\binmergetool.zh-Hant.txt" />
    <None Include="Assets\Art\Projectiles\VFX_Klaus\ReadMe.txt" />
    <None Include="Assets\Shader\CityBuilding.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\images.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\xdiff.ja.txt" />
    <None Include="Library\PackageCache\com.unity.textmeshpro@3.0.9\PackageConversionData.json" />
    <None Include="Library\PackageCache\com.unity.modules.unitywebrequestassetbundle@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\configurehelper.ja.txt" />
    <None Include="Assets\Art\Projectiles\VFX_Klaus\Shaders\Fx_explosion_apb.shader" />
    <None Include="Assets\Shader\Augment-Memories.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\differences.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\i3.en.txt" />
    <None Include="Library\PackageCache\com.unity.ide.visualstudio@2.0.21\package.json" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\ValidationConfig.json" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\package.json" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface-Mobile.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\images.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\configurehelper.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.modules.imageconversion@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.textmeshpro@3.0.9\ValidationExceptions.json" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\package.json" />
    <None Include="Library\PackageCache\com.unity.feature.development@1.0.1\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\plastic-gui.en.txt" />
    <None Include="Library\PackageCache\com.unity.modules.unityanalytics@1.0.0\package.json" />
    <None Include="Assets\Art\Projectiles\Shaders\Distortion.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\differences.zh-Hans.txt" />
    <None Include="Assets\Fonts\hanzi3000.txt" />
    <None Include="Assets\Art\Projectiles\Shaders\Blend_TwoSides.shader" />
    <None Include="Library\PackageCache\com.unity.ide.rider@3.0.36\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\commontypes.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.searcher@4.9.1\package.json" />
    <None Include="Library\PackageCache\com.unity.modules.video@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\ValidationConfig.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\guihelp.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.ide.rider@3.0.36\ValidationExceptions.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\workspaceserver.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\images.en.txt" />
    <None Include="Assets\Art\Projectiles\Shaders\Blend_CenterGlow.shader" />
    <None Include="Library\PackageCache\com.unity.test-framework@1.1.33\package.json" />
    <None Include="Library\PackageCache\com.unity.performance.profile-analyzer@1.2.2\package.json" />
    <None Include="Library\PackageCache\com.unity.modules.particlesystem@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\clientcommon.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\commontypes.es.txt" />
    <None Include="Assets\Art\Environment\Resource\LowPoly_01\Materials\Misc\Shader\POLYGON_Zombies.shader" />
    <None Include="Assets\Art\Projectiles\VFX_Klaus\Shaders\Fx_explosion_add.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\images.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\clientcommon.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\guihelp.ja.txt" />
    <None Include="Assets\Shader\Character04.shader" />
    <None Include="Library\PackageCache\com.unity.modules.physics2d@1.0.0\package.json" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\guihelp.en.txt" />
    <None Include="Library\PackageCache\com.unity.modules.vr@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.visualscripting@1.9.1\ValidationExceptions.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\i3.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\guihelp.zh-Hans.txt" />
    <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\differences.es.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF.shader" />
    <None Include="Assets\Art\Environment\Resource\LowPoly_01\Materials\Misc\Shader\SkyGradient.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\commontypes.en.txt" />
    <None Include="Library\PackageCache\com.unity.ide.visualstudio@2.0.21\ValidationExceptions.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\differences.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\i3.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\cm-help.en.txt" />
    <None Include="Library\PackageCache\com.unity.modules.wind@1.0.0\package.json" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile SSD.shader" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Library\PackageCache\com.unity.modules.umbra@1.0.0\package.json" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Properties.cginc" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\configurehelper.ko.txt" />
    <None Include="Assets\Shader\GaussianBlur.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\commontypes.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\basecommands.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\FileSystemWatcherLicense.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\commontypes.ja.txt" />
    <None Include="Assets\Art\Projectiles\Demo scene\HDRP and URP(LWRP).txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\binmergetool.zh-Hans.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Surface.cginc" />
    <None Include="Assets\Shader\CityBuilding02.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\guihelp.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\clientcommon.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.modules.audio@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\cm-help.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\binmergetool.ja.txt" />
    <None Include="Library\PackageCache\com.unity.modules.physics@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\images.ko.txt" />
    <None Include="Library\PackageCache\com.unity.modules.vehicles@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\workspaceserver.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\semantic.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\binmergetool.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\plastic-gui.ja.txt" />
    <None Include="Library\PackageCache\com.unity.testtools.codecoverage@1.2.4\package.json" />
    <None Include="Library\PackageCache\com.unity.modules.uielementsnative@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.modules.unitywebrequestwww@1.0.0\package.json" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
    <None Include="Library\PackageCache\com.unity.modules.unitywebrequestaudio@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\semantic.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\binmergetool.en.txt" />
    <None Include="Library\PackageCache\com.unity.textmeshpro@3.0.9\Editor Resources\Shaders\TMP_SDF_SSD.cginc" />
    <None Include="Assets\Shader\Character03.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\ValidationConfig.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\mergetool.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\clientcommon.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\cm.en.txt" />
    <None Include="Library\PackageCache\com.unity.modules.xr@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.textmeshpro@3.0.9\Editor Resources\Shaders\TMP_Properties.cginc" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\i3.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\configurehelper.en.txt" />
    <None Include="Assets\RES（Linshi）\Standard Assets\Effects\ImageEffects\Shaders\BlendModesOverlay.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\plastic-gui.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\semantic.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.textmeshpro@3.0.9\Editor Resources\Shaders\TMP_SDF Internal Editor.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\binmergetool.ko.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Masking.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\workspaceserver.zh-Hant.txt" />
    <None Include="Assets\RES（Linshi）\City Adventure\Plugins\ColorSuite\Shader\ColorSuite.shader" />
    <None Include="Library\PackageCache\com.unity.modules.director@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.ide.vscode@1.2.5\ValidationExceptions.json" />
    <None Include="Library\PackageCache\com.unity.visualscripting@1.9.1\ValidationConfig.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\plastic-gui.es.txt" />
    <None Include="Assets\Shader\SimplePhotoshopSaturation.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\plastic-gui.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\xdiff.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\basecommands.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.modules.terrain@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\clientcommon.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\mergetool.ja.txt" />
    <None Include="Library\PackageCache\com.unity.modules.androidjni@1.0.0\package.json" />
    <None Include="Assets\Art\Projectiles\Shaders\Add_CenterGlow.shader" />
    <None Include="Assets\Shader\PhotoshopSaturation.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\mergetool.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\cm.zh-Hans.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF Overlay.shader" />
    <None Include="Assets\Shader\SceneColorCorrection.shader" />
    <None Include="Assets\Art\Environment\Resource\PolygonCity\Change_Log.txt" />
    <None Include="Library\PackageCache\com.unity.textmeshpro@3.0.9\PackageConversionData_Assets.json" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Mobile.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\cm.ja.txt" />
    <None Include="Assets\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\mergetool.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.modules.animation@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\semantic.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\basecommands.zh-Hans.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile.shader" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\cm.ko.txt" />
    <None Include="Assets\Art\Environment\Resource\LowPoly_01\Materials\Misc\Shader\POLYGON_Triplanar.shader" />
    <None Include="Library\PackageCache\com.unity.modules.unitywebrequest@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.modules.uielements@1.0.0\package.json" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\configurehelper.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\xdiff.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Localization\differences.zh-Hant.txt" />
    <None Include="Assets\TextMesh Pro\Sprites\EmojiOne.json" />
    <None Include="Library\PackageCache\com.unity.modules.ui@1.0.0\package.json" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Sprite.shader" />
    <None Include="Assets\Shader\Character.shader" />
    <None Include="Library\PackageCache\com.unity.ide.vscode@1.2.5\package.json" />
    <Reference Include="UnityEngine">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsNativeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PackageManagerUIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.PackageManagerUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIServiceModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIServiceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.visualscripting@1.9.1\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="ReportGeneratorMerged">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.testtools.codecoverage@1.2.4\lib\ReportGenerator\ReportGeneratorMerged.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Unity.VisualScripting.Flow.Editor.csproj">
      <Project>{add2bf7a-fa0f-1d53-8081-8714be0e3f51}</Project>
      <Name>Unity.VisualScripting.Flow.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VSCode.Editor.csproj">
      <Project>{ebe9dd28-0ad1-4442-7cc7-d66bc43b25b6}</Project>
      <Name>Unity.VSCode.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.TextMeshPro.Editor.csproj">
      <Project>{28af33d0-fa31-4a77-e16e-c71b1856f748}</Project>
      <Name>Unity.TextMeshPro.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualStudio.Editor.csproj">
      <Project>{8e564bc1-9f71-3202-73a1-2c9a8890d405}</Project>
      <Name>Unity.VisualStudio.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Performance.Profile-Analyzer.Editor.csproj">
      <Project>{bf2d2f48-6d22-8535-549d-936606bc2481}</Project>
      <Name>Unity.Performance.Profile-Analyzer.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj">
      <Project>{4c7fa17c-c2d3-ce9d-29a8-1c2c9590c210}</Project>
      <Name>Unity.TestTools.CodeCoverage.Editor.OpenCover.Model</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Timeline.csproj">
      <Project>{fcc6bebf-fb22-7069-58a0-b5f120bd4e36}</Project>
      <Name>Unity.Timeline</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.Core.Editor.csproj">
      <Project>{2c2de4a4-7b2d-7e5d-30ed-a34951b4e80f}</Project>
      <Name>Unity.VisualScripting.Core.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.TextMeshPro.csproj">
      <Project>{acb91605-dacb-ff1e-2cbb-1625d29dbd63}</Project>
      <Name>Unity.TextMeshPro</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.State.Editor.csproj">
      <Project>{db85d88e-8284-7f70-7ba9-39eb395f5bce}</Project>
      <Name>Unity.VisualScripting.State.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Searcher.Editor.csproj">
      <Project>{4205c64c-3624-fd56-c8b3-e4921b2defb6}</Project>
      <Name>Unity.Searcher.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.SettingsProvider.Editor.csproj">
      <Project>{0cfaf96e-beb5-f910-c512-aa767c156488}</Project>
      <Name>Unity.VisualScripting.SettingsProvider.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Core.Runtime.csproj">
      <Project>{f88fa360-ff5c-aeed-37f6-f0542cf1b64c}</Project>
      <Name>Unity.RenderPipelines.Core.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj">
      <Project>{1d2cf181-8d79-5931-c983-55876ddbffb0}</Project>
      <Name>Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.Flow.csproj">
      <Project>{d0ab2354-acc7-4466-4509-b761d7e574d3}</Project>
      <Name>Unity.VisualScripting.Flow</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEditor.UI.csproj">
      <Project>{e2909ef1-2b33-1495-5d01-36a01c357a5b}</Project>
      <Name>UnityEditor.UI</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.PlasticSCM.Editor.csproj">
      <Project>{0e65ad46-1708-d9ff-f881-4968585179a5}</Project>
      <Name>Unity.PlasticSCM.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Rider.Editor.csproj">
      <Project>{1dbefbcc-34bc-b2e3-56e3-68c2bf3b9c11}</Project>
      <Name>Unity.Rider.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Core.ShaderLibrary.csproj">
      <Project>{0fbddd6b-b48e-86e0-ea53-12f90c520213}</Project>
      <Name>Unity.RenderPipelines.Core.ShaderLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEngine.UI.csproj">
      <Project>{e4e2ec11-4adb-d642-3e1b-0466e488a1e6}</Project>
      <Name>UnityEngine.UI</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.Core.csproj">
      <Project>{6608290a-01e5-01a1-07ae-0ad085a5f02f}</Project>
      <Name>Unity.VisualScripting.Core</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.EditorCoroutines.Editor.csproj">
      <Project>{47825be9-6a01-46fe-675f-40c2d51b8a91}</Project>
      <Name>Unity.EditorCoroutines.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.ShaderGraph.Editor.csproj">
      <Project>{93e2ff69-2dad-1bea-617f-d1043b169453}</Project>
      <Name>Unity.ShaderGraph.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.Shared.Editor.csproj">
      <Project>{ae0c0e04-88ba-7643-efaf-4536ae22f103}</Project>
      <Name>Unity.VisualScripting.Shared.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj">
      <Project>{d2b5c0f7-1e85-9592-25d0-d904544f5744}</Project>
      <Name>Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Timeline.Editor.csproj">
      <Project>{f1f80ce1-e189-76ad-779c-9bcd0b3b462d}</Project>
      <Name>Unity.Timeline.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.TestTools.CodeCoverage.Editor.csproj">
      <Project>{7d334f9c-dd83-a324-5cb4-dab7a526597b}</Project>
      <Name>Unity.TestTools.CodeCoverage.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Core.Editor.csproj">
      <Project>{b922fb10-9260-3133-adad-d58c076091c9}</Project>
      <Name>Unity.RenderPipelines.Core.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.State.csproj">
      <Project>{d2dfc30e-7747-1232-d315-150e2edfe4cd}</Project>
      <Name>Unity.VisualScripting.State</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
