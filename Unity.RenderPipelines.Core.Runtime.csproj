﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>9.0</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{f88fa360-ff5c-aeed-37f6-f0542cf1b64c}</ProjectGuid>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Unity.RenderPipelines.Core.Runtime</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\Unity.RenderPipelines.Core.Runtime\</OutputPath>
    <DefineConstants>UNITY_2021_3_31;UNITY_2021_3;UNITY_2021;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_RUNTIME_PERMISSIONS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;PLATFORM_ANDROID;TEXTCORE_1_0_OR_NEWER;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;ENABLE_ETC_COMPRESSION;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;USC_UNITY_PIPELINE_LEGACY;CROSS_PLATFORM_INPUT;MOBILE_INPUT;ENABLE_VR_MODULE;ENABLE_XR_MODULE;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169,0649</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerVector2.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Textures\RTHandle.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Camera\FreeCamera.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderGraph\RenderGraphPass.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\CoreProfileId.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\DebugUI.Panel.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\CoreAttributes.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderGraph\RenderGraphResourceTexture.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\ObservableList.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Textures\Texture2DAtlasDynamic.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeVolumeBakingProcessSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\LookDev\IDataProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerMessageBox.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Textures\DepthBits.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeVolumePerSceneData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeReferenceVolume.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerToggle.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\PostProcessing\LensFlareComponentSRP.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\ShaderGenerator\ShaderGeneratorAttributes.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\CoreMatrixUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\CommonStructs.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\Blitter.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Inputs\InputRegistering.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeReferenceVolumeProfile.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerWidget.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\ColorUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\DebugManager.Actions.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\PostProcessing\LensFlareCommonSRP.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Volume\VolumeStack.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderGraph\RenderGraphResourceRendererList.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\IVirtualTexturingEnabledRenderPipeline.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerEnumField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\ConstantBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerContainer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerVBox.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerToggleHistory.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderGraph\RenderGraphResourcePool.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\CommandBufferPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderGraph\RenderGraphProfileId.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\DelegateUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Textures\BufferedRTHandleSystem.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Textures\RTHandleSystem.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Textures\TextureXR.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerPersistentCanvas.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ShaderVariablesProbeVolumes.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\SceneRenderPipeline.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Documentation.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerEnumHistory.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeReferenceVolume.Debug.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\CoreRenderPipelinePreferences.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\ComponentSingleton.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\TextureCurve.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\XRUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Camera\CameraSwitcher.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\DebugShapes.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderGraph\RenderGraphResourceRegistry.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderGraph\RenderGraphLogger.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\HableCurve.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\ProfilingScope.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\ObjectPools.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Volume\VolumeProfile.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\XRGraphics.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\ResourceReloader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerVector3.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Textures\PowerOfTwoTextureAtlas.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerIndirectToggle.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderGraph\RenderGraphObjectPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\XR\XRGraphicsAutomatedTests.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\MousePositionDebug.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Volume\VolumeParameter.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\BitArray.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeVolumeAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\HaltonSequence.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerUIntField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerFloatField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeBrickIndex.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerPanel.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeBrickPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Textures\Texture2DAtlas.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Volume\VolumeComponent.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\TileLayoutUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\UIFoldout.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\MaterialQuality.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\CoreUnsafeUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerValue.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\DebugUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\DynamicArray.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\SphericalHarmonics.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerHBox.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerIntField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\SerializableEnum.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RendererList\RendererList.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerVector4.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerIndirectFloatField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Volume\IVolume.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderGraph\RenderGraphResourceComputeBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerBitField.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderGraph\RenderGraphBuilder.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\DebugUpdater.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerCanvas.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderGraph\RenderGraphDefaultResources.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerColor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeVolumeSceneData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderPipeline\RenderPipelineResources.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\DebugManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerRow.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\DynamicResolutionHandler.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\PostProcessing\LensFlareDataSRP.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\DebugUI.Containers.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeVolume.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\ShaderLibrary\Sampling\Hammersley.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\CameraCaptureBridge.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\FSRUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Volume\VolumeManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Textures\RTHandles.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lights\LightAnchor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderGraph\RenderGraphResources.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\SerializedDictionary.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Textures\MSAASamples.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\MeshGizmo.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\CoreUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\RenderGraph\RenderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerButton.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\AssemblyInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Volume\Volume.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeIndexOfIndices.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\ListBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeVolumePositioning.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\Prefabs\Scripts\DebugUIHandlerFoldout.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\GlobalDynamicResolutionSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Common\IAdditionalData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\ArrayExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Debugging\DebugUI.Fields.cs" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\ShaderLibrary\Sampling\Hammersley.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\PostProcessing\Shaders\LensFlareCommon.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\PostProcessing\Shaders\ffx\ffx_a.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\Blit.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\FallbackShader.shader" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\DecodeSH.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeReferenceVolume.Debug.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ProbeVolume.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\PostProcessing\Shaders\ffx\ffx_fsr1.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\PostProcessing\Shaders\ffx\ffx_cas.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\PostProcessing\Shaders\ffx\ffx_lpm.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Lighting\ProbeVolume\ShaderVariablesProbeVolumes.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Utilities\BlitColorAndDepth.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\PostProcessing\Shaders\FSRCommon.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.core@12.1.12\Runtime\Unity.RenderPipelines.Core.Runtime.asmdef" />
    <Reference Include="UnityEngine">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsNativeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PackageManagerUIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.PackageManagerUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIServiceModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIServiceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\AndroidPlayer\UnityEditor.Android.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\unityplastic.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.visualscripting@1.9.1\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.visualscripting@1.9.1\Editor\VisualScripting.Core\EditorAssetResources\Unity.VisualScripting.TextureAssets.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.visualscripting@1.9.1\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll</HintPath>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\log4netPlastic.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.visualscripting@1.9.1\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="ReportGeneratorMerged">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.testtools.codecoverage@1.2.4\lib\ReportGenerator\ReportGeneratorMerged.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>G:\2021.3.31f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="UnityEditor.UI.csproj">
      <Project>{e2909ef1-2b33-1495-5d01-36a01c357a5b}</Project>
      <Name>UnityEditor.UI</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEngine.UI.csproj">
      <Project>{e4e2ec11-4adb-d642-3e1b-0466e488a1e6}</Project>
      <Name>UnityEngine.UI</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
