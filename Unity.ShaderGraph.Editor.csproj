﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>9.0</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{93e2ff69-2dad-1bea-617f-d1043b169453}</ProjectGuid>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Unity.ShaderGraph.Editor</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\Unity.ShaderGraph.Editor\</OutputPath>
    <DefineConstants>UNITY_2021_3_31;UNITY_2021_3;UNITY_2021;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_RUNTIME_PERMISSIONS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;PLATFORM_ANDROID;TEXTCORE_1_0_OR_NEWER;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;ENABLE_ETC_COMPRESSION;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;USC_UNITY_PIPELINE_LEGACY;CROSS_PLATFORM_INPUT;MOBILE_INPUT;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169,0649</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Advanced\LengthNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\SampleTexture2DNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Matrix2ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Texture2DShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\CalculateLevelOfDetailTexture2DNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\ShaderGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Mask\ColorMaskNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Attributes\SubTargetFilterAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Colors\CustomColorData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\BuiltInStructs.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\ShaderValueType.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Procedural\Shape\RoundedPolygonNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Basic\Vector1Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Blackboard\SGBlackboardCategory.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Manipulators\ElementResizer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\ShaderDropdown.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\UV\TilingAndOffsetNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\NormalMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\ResizableElement.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\UV\ParallaxMappingNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\WindowDockingLayout.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Importers\ShaderSubGraphMetadata.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequirePositionPredisplacement.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\ICanChangeShaderGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\PropertyUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Implementation\GraphObject.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\TextureSamplerState.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Matrix\MatrixSplitNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\GraphEditorView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Geometry\VertexIDNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Util\TypeMapper.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\SerializationHelper.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\TextControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Vector\RotateAboutAxisNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Adjustment\InvertColorsNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\ShaderInput.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Texture2DArrayMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Vector2ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Round\FloorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Normal\NormalFromHeightNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Implementation\IHasDependencies.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Processors\PropertyCollector.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\SampleTexture2DArrayNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\KeywordDefinition.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\IPropertyFromNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\SubGraphNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Descriptors\SubShaderDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Blackboard\SGBlackboardRow.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\SamplerStateNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\SampleTexture2DNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\VectorShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\Cull.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IHasCustomDeprecationMessage.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\BlendOp.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\PooledHashSet.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Round\CeilingNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\ToggleDataPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\RenderType.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\RedirectNodeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\VirtualTextureShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\PropertyNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Descriptors\PassDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Trigonometry\ArccosineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Procedural\Noise\VoronoiNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\Texture3DAssetNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\ViewModels\ShaderInputViewModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Collections\DependencyCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Serialization\FakeJsonObject.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\HelpBoxRow.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\DynamicValueMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\AssetCallbacks\CreateShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Vector4ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderPreprocessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireScreenPosition.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\MinimalGraphData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\Logic\IsInfiniteNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Scene\FogNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\InspectorView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Interfaces\ISGViewModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\StickyNoteData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Trigonometry\CosineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Processors\CustomInterpolatorUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\CubemapPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Processors\MatrixNames.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\RenderQueue.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Channel\SwizzleNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Collections\FieldCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Range\RemapNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Attributes\SGPropertyDrawerAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Manipulators\ResizeSideHandle.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\CubemapMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\TargetResources\BlockFields.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\SerializableMesh.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\Graph\SlotReference.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\ShaderKeyword.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Geometry\ViewVectorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Blackboard\BlackboardUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Advanced\PosterizeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IGroupItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\EnumPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Descriptors\FieldDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Descriptors\IncludeDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Util\Documentation.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Matrix\Matrix2Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Descriptors\BlockFieldDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\ZTest.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\PreviewProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\UV\PolarCoordinatesNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\PreviewManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\NodeClassCache.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Basic\IntegerNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Blackboard\SGBlackboardField.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\BuiltInProperties.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\BuiltInFields.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Collections\PragmaCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\MeshDeformation\ComputeDeformNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\GraphSetup.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\RedirectNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Advanced\ReciprocalNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\DefaultShaderIncludes.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Normal\NormalReconstructZNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\AbstractShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\Texture3DControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Importers\ShaderSubGraphImporterEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireTransform.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Util\IndexSet.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Advanced\ModuloNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\UVSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Range\OneMinusNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Utility\ColorspaceConversion.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Manipulators\Draggable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\VirtualTextureMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\KeywordShaderStage.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Geometry\UVNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\IdentifierField.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireVertexColor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\IncludeLocation.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Extensions\StencilExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Adjustment\HueNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\FunctionRegistry.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Trigonometry\SineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Vector\ProjectionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Gradient\BlackbodyNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\Texture2DPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\VertexColorMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\AbstractMaterialNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequirePosition.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\Logic\IsFrontFaceNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Procedural\Noise\GradientNoiseNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Blend\BlendMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\SubGraphOutputNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\CubemapShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Procedural\Noise\SimpleNoiseNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Collections\DefineCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\TargetResources\FieldDependencies.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\Texture3DPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Scene\SceneDepthNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\GraphUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Descriptors\StencilDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Scene\CameraNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\SearchWindowProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\UnlitMasterNode1.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Contexts\TargetPropertyGUIContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\DropdownNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\SampleVirtualTextureNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Collections\IncludeCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Attributes\GenerateBlocksAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Utils\TargetUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IGeneratesFunction.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Scene\ObjectNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\PrecisionUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Advanced\NormalizeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Descriptors\KeywordDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Importers\ShaderGraphMetadata.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\SlotValue.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\Logic\BranchNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\DielectricSpecularControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Manipulators\ResizeBorderFrame.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Range\FractionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Attributes\InspectableAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Basic\SubtractNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\GraphData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Lighting\ReflectionProbeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Wave\TriangleWaveNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Lighting\BakedGINode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Trigonometry\HyperbolicCosineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Colors\CategoryColors.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\ChannelEnumControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Serialization\JsonRef.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Geometry\VertexColorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Blend\BlendNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Collections\RenderStateCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\MaterialGraphEditWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Procedural\CheckerboardNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\Logic\AllNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\FloatField.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Geometry\ScreenPositionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\Logic\ComparisonNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\OutputMetadata.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\LegacyUnknownTypeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireBitangent.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Matrix\TransformationMatrixNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\GraphCode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\ZWrite.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Trigonometry\RadiansToDegreesNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Matrix\MatrixConstructionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\GradientInputMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\TabbedView\TabButton.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\SubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Channel\SplitNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Range\MinimumNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\InstancingOptions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\NormalMapSpace.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\TargetResources\StructFields.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\ReorderableTextListView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Processors\ShaderStringBuilder.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\ShaderModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\ShaderGraphVfxAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\ColorControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Procedural\Shape\EllipseNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\ToggleControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\BooleanSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\UV\TwirlNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\ContextView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\Blend.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\MasterPreviewView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Serialization\RefValueEnumerable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\GatherTexture2DNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\KeywordScope.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Matrix\MatrixTransposeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\ViewModels\BlackboardCategoryViewModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\IntegerControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\PBRMasterNode1.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Normal\NormalStrengthNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireViewDirection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\TextureStackNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Range\ClampNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\GenerationMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Scene\EyeIndexNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\Vector3PropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\Vector4PropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Importers\ShaderGraphImporter.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Normal\NormalBlendNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Vector\TransformNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Colors\IColorProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\AssetCallbacks\CreateShaderSubGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\PreviewMode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Geometry\InstanceIDNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\ColorPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\IMaterialGraphAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireDepthTexture.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\GroupData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\ProceduralVirtualTextureNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Interfaces\IResizable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGUI\BuiltInLitGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\UV\SpherizeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\ViewDirectionMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\CodeFunctionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\PositionMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Gradient\SampleGradientNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\ShaderGUI\GenericShaderGraphMaterialGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Contexts\TargetActiveBlockContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\ColorRGBMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\PositionNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controllers\BlackboardCategoryController.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGUI\BuiltInUnlitGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Basic\AddNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Basic\Vector4Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Processors\ActiveFields.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\TitleAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\TextureArraySlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Util\ListUtilities.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\EdgeConnectorListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\SubGraph\SubGraphAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Util\UIUtilities.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\KeywordType.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Serialization\MultiJsonInternal.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\PropertyConnectionStateSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\SpaceMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controllers\SGController.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Collections\KeywordCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Trigonometry\TangentNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Basic\TimeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Matrix4ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\SampleTexture2DLODNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Serialization\MultiJson.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\TextureSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controllers\ShaderInputViewController.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Actions\GraphViewActions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\UV\ParallaxOcclusionMappingNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\StickyNote.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\IControlAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Manipulators\WindowDraggable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\SplitTextureTransformNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Processors\ShaderGeneratorNames.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Attributes\ContextFilterableAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Util\AssertHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\StackPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\MaterialGraphPreviewGenerator.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Trigonometry\HyperbolicTangentNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\KeywordDependentCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Trigonometry\ArctangentNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\AbstractMaterialGraphAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Channel\CombineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\GradientUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Serialization\JsonData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Blackboard\BlackboardInputInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\AssemblyInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\KeywordUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Extensions\FieldExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\Graph\IEdge.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Serialization\MultiJsonEntry.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\GradientEdge.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Vector1ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\ShaderPort.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\IMasterNode1.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Vector3ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Adjustment\ReplaceColorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\VFXTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Data\ConditionalField.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Advanced\AbsoluteNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Basic\DivideNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Vector\SphereMaskNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\CubemapControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\GraphDataPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\Graph\INode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IGraphDataAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\ShaderInput0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\SerializableVirtualTexture.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\NodeSettingsView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\SamplerStateShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Vector\RejectionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\BuiltInMetadata.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\Graph\DrawState.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\IdentifierControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Importers\ShaderGraphImporterEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\IHasMetaData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\UV\FlipbookNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\ShaderGraphPreferences.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Basic\PowerNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\SampleTexture3DNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\AssetPostProcessors\ShaderGraphMaterialsUpdater.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Matrix3MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\ColorShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\AbstractMaterialNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\LightmappingShaderProperties.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Matrix\MatrixDeterminantNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\Edge0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\ShaderGraphAnalytics.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Targets\BuiltInTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Derivative\DDYNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\AssetPostProcessors\MaterialPostprocessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\AssetCallbacks\CreateUnlitShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Collections\AdditionalCommandCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\Graph\GraphDrawingData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Adjustment\WhiteBalanceNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Basic\Vector2Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireNormal.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\BlockNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\ShaderStage.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\SamplerStateNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\ILegacyTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Geometry\BitangentVectorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Blackboard\SGBlackboard.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\NormalDropOffSpace.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\GradientSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Collections\PassCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Util\MessageManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\Platform.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Implementation\Edge.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Geometry\NormalVectorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Mask\ChannelMaskNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\PropertyRow.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\AssetVersion.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Basic\SquareRootNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Interpolation\SmoothstepNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\MeshDeformation\LinearBlendSkinningNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Descriptors\RenderStateDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\CustomFunctionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\FunctionMultiInput.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Procedural\Shape\PolygonNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\DefaultControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\Identifier.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\GroupData0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\HlslFunctionView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Targets\BuiltInLitSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\AssetCallbacks\CreateLitShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\SliderControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\INodeModificationListener.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IGeneratesBodyCode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\ViewModels\BlackboardViewModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Wave\SawtoothWaveNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Targets\BuiltInUnlitSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\FormerNameAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Matrix3ShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\MaterialGraphView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\SampleVirtualTextureNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Target.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\GradientMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\TextUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\TextureControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\ProceduralVirtualTextureNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireTime.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\PBR\MetalReflectanceNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\PropertyType.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Serialization\RefDataEnumerable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Contexts\TargetSetupContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireCameraOpaqueTexture.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Vector\DotProductNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Normal\NormalUnpackNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireFaceSign.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Matrix4MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\ButtonControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Procedural\Shape\RoundedRectangleNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Basic\BooleanNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\SampleTexture2DArrayNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\UV\RadialShearNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\GraphDataUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\GradientControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Contexts\TargetFieldContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\Vector2PropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Vector\DistanceNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Derivative\DDXYNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\PropertySheet.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Matrix\Matrix4Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Collections\AssetCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Procedural\Shape\RectangleNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Trigonometry\DegreesToRadiansNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\BooleanShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\GuidEncoder.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Importers\ShaderSubGraphImporter.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Util\CreateSerializableGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Util\ValueUtilities.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\Graph\IOnAssetEnabled.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\KeywordNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\KeywordCollector.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Basic\Vector3Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\Texture2DArrayAssetNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\GeometryNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Trigonometry\HyperbolicSineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Interpolation\InverseLerpNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\UV\TriplanarNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Derivative\DDXNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\DropdownPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Manipulators\Scrollable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGUI\BaseShaderGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Serialization\JsonObject.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Vector2MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\ReorderableSlotListView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Vector3MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\ShaderInputPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Geometry\TangentVectorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Data\DropdownEntry.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Implementation\HasDependenciesAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controllers\BlackboardController.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\SerializableTextureArray.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Range\MaximumNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\PropertyConnectionStateMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\VisualEffectMasterNode1.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\BooleanMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\TargetResources\Fields.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Range\SaturateNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\FloatPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Enumerations\Precision.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Util\TypeMapping.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\IMaterialSlotHasValue.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Data\FieldCondition.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Lighting\AmbientNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\CubemapAssetNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Basic\MultiplyNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawerUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\PopupControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\LabelSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Attributes\SRPFilterAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\GradientShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\PooledList.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\IShaderNodeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Interfaces\ISGControlledElement.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Channel\FlipNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Adjustment\SaturationNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\CustomFunctionNodePropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Scene\ScreenNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\DataStore.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\SearchWindowAdapter.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\ParentGroupChange.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\TexelSizeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\CategoryData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\Logic\OrNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Util\CopyPasteGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Vector\FresnelEffectNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\ColorRGBSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Wave\SquareWaveNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Util\FileUtilities.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireVertexSkinning.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\SerializableGuid.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Vector\CrossProductNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Scene\SceneColorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Enumerations\StructFieldOptions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\ScreenSpaceType.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Descriptors\AdditionalCommandDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\ShaderGraphRequirementsPerKeyword.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\GraphConcretization.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\SerializableCubemap.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Targets\BuiltInSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Range\RandomRangeNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Round\StepNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\ChannelMixerControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Round\TruncateNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\PortInputView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\CubemapSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\ColorMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Matrix\Matrix3Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\MatrixPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\PBR\DielectricSpecularNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\CubemapInputMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\ScreenPositionSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IInspectable.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\PreviewTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\Texture3DSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\SampleCubemapNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\PreviewNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\SerializableTexture.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\MatrixShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Implementation\SlotType.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\TextureArrayControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\GradientPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\MaterialEditor\ShaderGraphPropertyDrawers.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\ObjectControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Colors\NoColors.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Colors\ColorManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Attributes\BuiltinKeywordAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\Logic\NotNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Collections\StructCollection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Texture3DInputMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\SubGraph\SubGraphOutputNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireVertexID.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\PreviewSceneResources.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\GraphValidation.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireTangent.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Texture3DMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\BoolPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\Logging.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Importers\ShaderGraphAssetPostProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\SlotValueTypeUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\DynamicVectorMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Interpolation\LerpNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Processors\ShaderSpliceUtil.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\GraphDataReadOnly.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\ContextData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Normal\NormalFromTextureNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Round\RoundNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\VirtualTextureInputMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\Texture2DAssetNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Processors\GenerationUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Adjustment\ChannelMixerNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Descriptors\StructDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Geometry\PositionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Matrix2MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Trigonometry\Arctangent2Node.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\Logic\NandNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Attributes\NeverAllowedByTargetAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\SlotReference0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Vector1MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Texture\SampleRawCubemapNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Descriptors\PragmaDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\ViewModels\InspectorViewModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\SpriteUnlitMasterNode1.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\ShaderGraphProjectSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\PositionSource.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Texture3DShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Data\FieldDependency.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\IShaderPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMaySupportVFX.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\ColorSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Advanced\ExponentialNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\MultiIntegerSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Colors\PrecisionColors.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\TabbedView\TabbedView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\UVMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Round\SignNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\SamplerStateMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Controls.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Serialization\SerializationExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\MaterialNodeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Data\KeywordEntry.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\ShaderGraphRequirements.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\IMayRequireMeshUV.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\SerializableGuid.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\BuiltInStructFields.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Advanced\ReciprocalSquareRootNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\DynamicMatrixMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Geometry\ViewDirectionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Interfaces\NeededCoordinateSpace.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\Logic\IsNanNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\BitangentMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Vector\ReflectionNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Processors\GraphCompilationResult.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Implementation\NodeUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Vector4MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Interfaces\IRectInterface.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Advanced\NegateNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\VectorControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\MaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Basic\ConstantNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\CustomInterpolatorNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\TargetResources\Structs.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Texture2DArrayShaderProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\TextPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Processors\Generator.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\Logic\BranchOnInputConnection.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\GraphData0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\Logic\AnyNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\SpriteLitMasterNode1.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\IntegerPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Trigonometry\ArcsineNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\UV\RotateNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\Slots\MultiFloatSlotControlView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\EnumConversionControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\RedirectNodeData.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\PropertyNodeView.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Wave\NoiseSineWaveNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\ChannelEnumMaskControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Interface\IConditional.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Gradient\GradientNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Inspector\PropertyDrawers\Texture2DArrayPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Util\CompatibilityExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Math\Advanced\LogNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Adjustment\ContrastNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Extensions\IConditionalExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\AssetCallbacks\CreateVFXShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\TangentMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Artistic\Filter\DitherNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Texture2DArrayInputMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\ScreenPositionMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Texture2DMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Util\UvChannel.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Colors\UserColors.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\AbstractMaterialNode0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Basic\SliderNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Controls\EnumControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Drawing\Views\GraphSubWindow.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Legacy\StickyNoteData0.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Graphs\Texture2DInputMaterialSlot.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Utility\Logic\AndNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Data\Nodes\Input\Basic\ColorNode.cs" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\SGBlackboard.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\DeclareDepthTexture.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Shaders\BlitNoAlpha.shader" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\UnlitPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\UXML\Blackboard\SGBlackboardRow.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\TabbedView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\IntegerControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\ShaderTypes.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\HelpBoxRow.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Core.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\SurfaceInput.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Resizable.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\MetaInput.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\ScreenPositionSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\PixelCacheProfiler.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\CubemapSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\MasterPreviewView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Templates\SharedCode.template.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\RedirectNode.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\BuildInputData.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\PBRDeferredPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\SSAO.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\TextureArraySlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\PBRForwardAddPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Particles.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Templates\BuildVertexDescriptionInputs.template.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\ShaderPort.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\PropertyNameReferenceField.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Templates\BuildSurfaceDescriptionInputs.template.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\UXML\Blackboard\SGBlackboardField.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Lighting.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\LegacySurfaceVertex.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\GraphSubWindow.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\UXML\TabButton.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\MaterialNodeView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\UXML\StickyNote.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\SliderControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\ShadowCasterPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\BuiltInDOTSInstancing.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\ShaderPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\UnityGBuffer.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\PropertyNodeView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Shim\InputsShim.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\MaterialGraph.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\PopupControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\GradientSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\ReorderableSlotListView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\EnumConversionControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\ColorRGBASlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\DielectricSpecularControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\TextControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\HlslFunctionView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\CustomSlotLabelField.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Templates\ShaderPass.template" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Shadows.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\UXML\NodeSettings.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\BooleanSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\ShaderGraphFunctions.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\InspectorView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\Varyings.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Selectable.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\SurfaceData.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\MultiFloatControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\EnumControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\UXML\Blackboard\SGBlackboardCategory.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\GraphEditorView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\MultiFloatSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\LightingMetaPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\StickyNote.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\NodeSettings.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\TextureSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\UVSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\ChannelMixerControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\ParticlesInstancing.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\MaterialGraphView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\ColorMode.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Input.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\PropertyRow.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Shim\SurfaceShaderProxy.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\ToggleControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Deprecated.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\MultiIntegerSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Shaders\FallbackError.shader" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Shim\Shims.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\ColorControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\ShaderVariablesFunctions.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\PortInputView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\UnityInput.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Shaders\Checkerboard.shader" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\Shim\HLSLSupportShim.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\SpriteUnlitPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\PropertySheet.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\TabButtonStyles.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\UXML\GraphInspector.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Templates\PassMesh.template" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\ChannelEnumMaskControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\Texture3DSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\GradientControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\ChannelEnumControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\DepthOnlyPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\UXML\GraphSubWindow.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\PBRGBufferPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\UXML\Blackboard\SGBlackboard.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\LegacyBuilding.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\DeclareNormalsTexture.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\UXML\Resizable.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Unity.ShaderGraph.Editor.asmdef" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\UXML\PixelCacheProfiler.uxml" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Resources\Styles\Controls\ColorRGBSlotControlView.uss" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\Editor\ShaderGraph\Includes\PBRForwardPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\DeclareOpaqueTexture.hlsl" />
    <None Include="Library\PackageCache\com.unity.shadergraph@12.1.12\Editor\Generation\Targets\BuiltIn\ShaderLibrary\ShaderVariablesFunctions.deprecated.hlsl" />
    <Reference Include="UnityEngine">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.NVIDIAModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.NVIDIAModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsNativeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PackageManagerUIModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.PackageManagerUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIServiceModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIServiceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>G:\2021.3.31f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\AndroidPlayer\UnityEditor.Android.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\unityplastic.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.visualscripting@1.9.1\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.visualscripting@1.9.1\Editor\VisualScripting.Core\EditorAssetResources\Unity.VisualScripting.TextureAssets.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.visualscripting@1.9.1\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll</HintPath>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.collab-proxy@2.1.0\Lib\Editor\PlasticSCM\log4netPlastic.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.visualscripting@1.9.1\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="ReportGeneratorMerged">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.testtools.codecoverage@1.2.4\lib\ReportGenerator\ReportGeneratorMerged.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>G:\Work\XRG_SVN\Mini Game\Library\PackageCache\com.unity.ext.nunit@1.0.6\net35\unity-custom\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>G:\2021.3.31f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>G:\2021.3.31f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Unity.ShaderGraph.Utilities.csproj">
      <Project>{cfbd0636-58d0-9071-5b38-2593df66cc35}</Project>
      <Name>Unity.ShaderGraph.Utilities</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Searcher.Editor.csproj">
      <Project>{4205c64c-3624-fd56-c8b3-e4921b2defb6}</Project>
      <Name>Unity.Searcher.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Core.Runtime.csproj">
      <Project>{f88fa360-ff5c-aeed-37f6-f0542cf1b64c}</Project>
      <Name>Unity.RenderPipelines.Core.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Core.Editor.csproj">
      <Project>{b922fb10-9260-3133-adad-d58c076091c9}</Project>
      <Name>Unity.RenderPipelines.Core.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEditor.UI.csproj">
      <Project>{e2909ef1-2b33-1495-5d01-36a01c357a5b}</Project>
      <Name>UnityEditor.UI</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEngine.UI.csproj">
      <Project>{e4e2ec11-4adb-d642-3e1b-0466e488a1e6}</Project>
      <Name>UnityEngine.UI</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEngine.TestRunner.csproj">
      <Project>{12c5ea48-a159-1e07-25be-9c7d4364f064}</Project>
      <Name>UnityEngine.TestRunner</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEditor.TestRunner.csproj">
      <Project>{725a3699-1238-328c-daad-7c09bbd4b914}</Project>
      <Name>UnityEditor.TestRunner</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
