﻿
Microsoft Visual Studio Solution File, Format Version 11.00
# Visual Studio 2010
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UnityEngine.UI", "UnityEngine.UI.csproj", "{e4e2ec11-4adb-d642-3e1b-0466e488a1e6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.RenderPipelines.Core.Runtime", "Unity.RenderPipelines.Core.Runtime.csproj", "{f88fa360-ff5c-aeed-37f6-f0542cf1b64c}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UnityEngine.TestRunner", "UnityEngine.TestRunner.csproj", "{12c5ea48-a159-1e07-25be-9c7d4364f064}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.VisualScripting.Flow", "Unity.VisualScripting.Flow.csproj", "{d0ab2354-acc7-4466-4509-b761d7e574d3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.VisualScripting.State", "Unity.VisualScripting.State.csproj", "{d2dfc30e-7747-1232-d315-150e2edfe4cd}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.VisualScripting.Core", "Unity.VisualScripting.Core.csproj", "{6608290a-01e5-01a1-07ae-0ad085a5f02f}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.TextMeshPro", "Unity.TextMeshPro.csproj", "{acb91605-dacb-ff1e-2cbb-1625d29dbd63}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp", "Assembly-CSharp.csproj", "{485e3fb3-b411-5e2a-5ded-b6f620d2d303}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.Timeline", "Unity.Timeline.csproj", "{fcc6bebf-fb22-7069-58a0-b5f120bd4e36}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.RenderPipelines.Core.ShaderLibrary", "Unity.RenderPipelines.Core.ShaderLibrary.csproj", "{0fbddd6b-b48e-86e0-ea53-12f90c520213}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj", "{d2b5c0f7-1e85-9592-25d0-d904544f5744}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.PlasticSCM.Editor", "Unity.PlasticSCM.Editor.csproj", "{0e65ad46-1708-d9ff-f881-4968585179a5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.VisualScripting.Core.Editor", "Unity.VisualScripting.Core.Editor.csproj", "{2c2de4a4-7b2d-7e5d-30ed-a34951b4e80f}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.VisualScripting.Flow.Editor", "Unity.VisualScripting.Flow.Editor.csproj", "{add2bf7a-fa0f-1d53-8081-8714be0e3f51}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.ShaderGraph.Editor", "Unity.ShaderGraph.Editor.csproj", "{93e2ff69-2dad-1bea-617f-d1043b169453}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.VisualStudio.Editor", "Unity.VisualStudio.Editor.csproj", "{8e564bc1-9f71-3202-73a1-2c9a8890d405}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.Timeline.Editor", "Unity.Timeline.Editor.csproj", "{f1f80ce1-e189-76ad-779c-9bcd0b3b462d}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.VisualScripting.State.Editor", "Unity.VisualScripting.State.Editor.csproj", "{db85d88e-8284-7f70-7ba9-39eb395f5bce}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.TextMeshPro.Editor", "Unity.TextMeshPro.Editor.csproj", "{28af33d0-fa31-4a77-e16e-c71b1856f748}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.RenderPipelines.Core.Editor", "Unity.RenderPipelines.Core.Editor.csproj", "{b922fb10-9260-3133-adad-d58c076091c9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.VisualScripting.SettingsProvider.Editor", "Unity.VisualScripting.SettingsProvider.Editor.csproj", "{0cfaf96e-beb5-f910-c512-aa767c156488}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UnityEditor.TestRunner", "UnityEditor.TestRunner.csproj", "{725a3699-1238-328c-daad-7c09bbd4b914}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.TestTools.CodeCoverage.Editor", "Unity.TestTools.CodeCoverage.Editor.csproj", "{7d334f9c-dd83-a324-5cb4-dab7a526597b}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.Rider.Editor", "Unity.Rider.Editor.csproj", "{1dbefbcc-34bc-b2e3-56e3-68c2bf3b9c11}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.Performance.Profile-Analyzer.Editor", "Unity.Performance.Profile-Analyzer.Editor.csproj", "{bf2d2f48-6d22-8535-549d-936606bc2481}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.Searcher.Editor", "Unity.Searcher.Editor.csproj", "{4205c64c-3624-fd56-c8b3-e4921b2defb6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp-Editor", "Assembly-CSharp-Editor.csproj", "{70bfd526-ccec-c017-fa15-a76f334f86de}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.VSCode.Editor", "Unity.VSCode.Editor.csproj", "{ebe9dd28-0ad1-4442-7cc7-d66bc43b25b6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UnityEditor.UI", "UnityEditor.UI.csproj", "{e2909ef1-2b33-1495-5d01-36a01c357a5b}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj", "{4c7fa17c-c2d3-ce9d-29a8-1c2c9590c210}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.EditorCoroutines.Editor", "Unity.EditorCoroutines.Editor.csproj", "{47825be9-6a01-46fe-675f-40c2d51b8a91}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.Settings.Editor", "Unity.Settings.Editor.csproj", "{bdf4f435-ae60-9c02-18b5-581fe3be23d8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.CollabProxy.Editor", "Unity.CollabProxy.Editor.csproj", "{7582682f-3891-d331-5df8-b0b63f74bad3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.ShaderGraph.Utilities", "Unity.ShaderGraph.Utilities.csproj", "{cfbd0636-58d0-9071-5b38-2593df66cc35}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj", "{1d2cf181-8d79-5931-c983-55876ddbffb0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.VisualScripting.Shared.Editor", "Unity.VisualScripting.Shared.Editor.csproj", "{ae0c0e04-88ba-7643-efaf-4536ae22f103}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.Searcher.EditorTests", "Unity.Searcher.EditorTests.csproj", "{cfaa3b05-c274-b214-4815-52d19864edb7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.TestTools.CodeCoverage.Editor.Compatibility", "Unity.TestTools.CodeCoverage.Editor.Compatibility.csproj", "{8f195d6a-ea1a-d5f1-9142-0f0cbf25bd26}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.TextMeshPro.Editor.Tests", "Unity.TextMeshPro.Editor.Tests.csproj", "{70594473-6ef9-45e1-d586-e7818142e4cd}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DocCodeExamples", "DocCodeExamples.csproj", "{dffe6afd-bb06-7131-2c9d-e3f12cc838ea}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.Performance.Profile-Analyzer.EditorTests", "Unity.Performance.Profile-Analyzer.EditorTests.csproj", "{d6759d4f-d05f-505b-561f-c209ea14fe12}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.Settings.Tests", "Unity.Settings.Tests.csproj", "{689ca3ff-7890-3c8d-d536-30e7b6f27bb7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UnityEngine.UI.Tests", "UnityEngine.UI.Tests.csproj", "{0da3030e-1a8e-0c3b-2781-ac4f53ea6316}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.TextMeshPro.Tests", "Unity.TextMeshPro.Tests.csproj", "{664384db-8f3b-b569-216b-1aad0604612e}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UnityEditor.UI.EditorTests", "UnityEditor.UI.EditorTests.csproj", "{dbbf633f-ae97-ee9a-0905-2747a7af2ac4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.RenderPipelines.Core.Editor.Tests", "Unity.RenderPipelines.Core.Editor.Tests.csproj", "{6811eb05-3cf3-daab-c64e-fa45d6fc851c}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.RenderPipelines.Core.Runtime.Tests", "Unity.RenderPipelines.Core.Runtime.Tests.csproj", "{aaad4da9-0af7-39c1-0178-208d161d8aee}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.ShaderGraph.Editor.Tests", "Unity.ShaderGraph.Editor.Tests.csproj", "{f8db05ed-f882-d1f3-00e5-ea2687fe67d7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{e4e2ec11-4adb-d642-3e1b-0466e488a1e6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{e4e2ec11-4adb-d642-3e1b-0466e488a1e6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{f88fa360-ff5c-aeed-37f6-f0542cf1b64c}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{f88fa360-ff5c-aeed-37f6-f0542cf1b64c}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{12c5ea48-a159-1e07-25be-9c7d4364f064}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{12c5ea48-a159-1e07-25be-9c7d4364f064}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{d0ab2354-acc7-4466-4509-b761d7e574d3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{d0ab2354-acc7-4466-4509-b761d7e574d3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{d2dfc30e-7747-1232-d315-150e2edfe4cd}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{d2dfc30e-7747-1232-d315-150e2edfe4cd}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6608290a-01e5-01a1-07ae-0ad085a5f02f}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6608290a-01e5-01a1-07ae-0ad085a5f02f}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{acb91605-dacb-ff1e-2cbb-1625d29dbd63}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{acb91605-dacb-ff1e-2cbb-1625d29dbd63}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{485e3fb3-b411-5e2a-5ded-b6f620d2d303}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{485e3fb3-b411-5e2a-5ded-b6f620d2d303}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{fcc6bebf-fb22-7069-58a0-b5f120bd4e36}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{fcc6bebf-fb22-7069-58a0-b5f120bd4e36}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0fbddd6b-b48e-86e0-ea53-12f90c520213}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0fbddd6b-b48e-86e0-ea53-12f90c520213}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{d2b5c0f7-1e85-9592-25d0-d904544f5744}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{d2b5c0f7-1e85-9592-25d0-d904544f5744}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0e65ad46-1708-d9ff-f881-4968585179a5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0e65ad46-1708-d9ff-f881-4968585179a5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2c2de4a4-7b2d-7e5d-30ed-a34951b4e80f}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2c2de4a4-7b2d-7e5d-30ed-a34951b4e80f}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{add2bf7a-fa0f-1d53-8081-8714be0e3f51}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{add2bf7a-fa0f-1d53-8081-8714be0e3f51}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{93e2ff69-2dad-1bea-617f-d1043b169453}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{93e2ff69-2dad-1bea-617f-d1043b169453}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8e564bc1-9f71-3202-73a1-2c9a8890d405}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8e564bc1-9f71-3202-73a1-2c9a8890d405}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{f1f80ce1-e189-76ad-779c-9bcd0b3b462d}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{f1f80ce1-e189-76ad-779c-9bcd0b3b462d}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{db85d88e-8284-7f70-7ba9-39eb395f5bce}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{db85d88e-8284-7f70-7ba9-39eb395f5bce}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28af33d0-fa31-4a77-e16e-c71b1856f748}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28af33d0-fa31-4a77-e16e-c71b1856f748}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{b922fb10-9260-3133-adad-d58c076091c9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{b922fb10-9260-3133-adad-d58c076091c9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0cfaf96e-beb5-f910-c512-aa767c156488}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0cfaf96e-beb5-f910-c512-aa767c156488}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{725a3699-1238-328c-daad-7c09bbd4b914}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{725a3699-1238-328c-daad-7c09bbd4b914}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7d334f9c-dd83-a324-5cb4-dab7a526597b}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7d334f9c-dd83-a324-5cb4-dab7a526597b}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1dbefbcc-34bc-b2e3-56e3-68c2bf3b9c11}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1dbefbcc-34bc-b2e3-56e3-68c2bf3b9c11}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{bf2d2f48-6d22-8535-549d-936606bc2481}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{bf2d2f48-6d22-8535-549d-936606bc2481}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4205c64c-3624-fd56-c8b3-e4921b2defb6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4205c64c-3624-fd56-c8b3-e4921b2defb6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{70bfd526-ccec-c017-fa15-a76f334f86de}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{70bfd526-ccec-c017-fa15-a76f334f86de}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ebe9dd28-0ad1-4442-7cc7-d66bc43b25b6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ebe9dd28-0ad1-4442-7cc7-d66bc43b25b6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{e2909ef1-2b33-1495-5d01-36a01c357a5b}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{e2909ef1-2b33-1495-5d01-36a01c357a5b}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4c7fa17c-c2d3-ce9d-29a8-1c2c9590c210}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4c7fa17c-c2d3-ce9d-29a8-1c2c9590c210}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47825be9-6a01-46fe-675f-40c2d51b8a91}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47825be9-6a01-46fe-675f-40c2d51b8a91}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{bdf4f435-ae60-9c02-18b5-581fe3be23d8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{bdf4f435-ae60-9c02-18b5-581fe3be23d8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7582682f-3891-d331-5df8-b0b63f74bad3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7582682f-3891-d331-5df8-b0b63f74bad3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{cfbd0636-58d0-9071-5b38-2593df66cc35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{cfbd0636-58d0-9071-5b38-2593df66cc35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1d2cf181-8d79-5931-c983-55876ddbffb0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1d2cf181-8d79-5931-c983-55876ddbffb0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ae0c0e04-88ba-7643-efaf-4536ae22f103}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ae0c0e04-88ba-7643-efaf-4536ae22f103}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{cfaa3b05-c274-b214-4815-52d19864edb7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{cfaa3b05-c274-b214-4815-52d19864edb7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8f195d6a-ea1a-d5f1-9142-0f0cbf25bd26}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8f195d6a-ea1a-d5f1-9142-0f0cbf25bd26}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{70594473-6ef9-45e1-d586-e7818142e4cd}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{70594473-6ef9-45e1-d586-e7818142e4cd}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{dffe6afd-bb06-7131-2c9d-e3f12cc838ea}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{dffe6afd-bb06-7131-2c9d-e3f12cc838ea}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{d6759d4f-d05f-505b-561f-c209ea14fe12}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{d6759d4f-d05f-505b-561f-c209ea14fe12}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{689ca3ff-7890-3c8d-d536-30e7b6f27bb7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{689ca3ff-7890-3c8d-d536-30e7b6f27bb7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0da3030e-1a8e-0c3b-2781-ac4f53ea6316}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0da3030e-1a8e-0c3b-2781-ac4f53ea6316}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{664384db-8f3b-b569-216b-1aad0604612e}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{664384db-8f3b-b569-216b-1aad0604612e}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{dbbf633f-ae97-ee9a-0905-2747a7af2ac4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{dbbf633f-ae97-ee9a-0905-2747a7af2ac4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6811eb05-3cf3-daab-c64e-fa45d6fc851c}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6811eb05-3cf3-daab-c64e-fa45d6fc851c}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{aaad4da9-0af7-39c1-0178-208d161d8aee}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{aaad4da9-0af7-39c1-0178-208d161d8aee}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{f8db05ed-f882-d1f3-00e5-ea2687fe67d7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{f8db05ed-f882-d1f3-00e5-ea2687fe67d7}.Debug|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
