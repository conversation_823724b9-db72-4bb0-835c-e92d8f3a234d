Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.31f1 (3409e2af086f) revision 3410402'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 32508 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
G:\2021.3.31f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
G:/Work/XRG_SVN/Mini Game
-logFile
Logs/AssetImportWorker0.log
-srvPort
58479
Successfully changed project path to: G:/Work/XRG_SVN/Mini Game
G:/Work/XRG_SVN/Mini Game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [34916] Host "[IP] ********** [Port] 0 [Flags] 2 [Guid] 240266842 [EditorId] 240266842 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-9EDR9P8) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [34916] Host "[IP] ********** [Port] 0 [Flags] 2 [Guid] 240266842 [EditorId] 240266842 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-9EDR9P8) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 70.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.31f1 (3409e2af086f)
[Subsystems] Discovering subsystems at path G:/2021.3.31f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path G:/Work/XRG_SVN/Mini Game/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 (ID=0x2504)
    Vendor:   NVIDIA
    VRAM:     12115 MB
    Driver:   32.0.15.7652
Initialize mono
Mono path[0] = 'G:/2021.3.31f1/Editor/Data/Managed'
Mono path[1] = 'G:/2021.3.31f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'G:/2021.3.31f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56804
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: G:/2021.3.31f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: G:/2021.3.31f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: G:/2021.3.31f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001300 seconds.
Native extension for WindowsStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 255 ms
Refreshing native plugins compatible for Editor in 76.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.752 seconds
Domain Reload Profiling:
	ReloadAssembly (753ms)
		BeginReloadAssembly (70ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (618ms)
			LoadAssemblies (70ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (73ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (21ms)
			SetupLoadedEditorAssemblies (492ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (324ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (77ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (58ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.001875 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 74.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.207 seconds
Domain Reload Profiling:
	ReloadAssembly (1208ms)
		BeginReloadAssembly (83ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (15ms)
		EndReloadAssembly (1061ms)
			LoadAssemblies (64ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (191ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (42ms)
			SetupLoadedEditorAssemblies (746ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (75ms)
				BeforeProcessingInitializeOnLoad (56ms)
				ProcessInitializeOnLoadAttributes (551ms)
				ProcessInitializeOnLoadMethodAttributes (45ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 1.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4112 Unused Serialized files (Serialized files now loaded: 0)
Unloading 25 unused Assets / (342.5 KB). Loaded Objects now: 4586.
Memory consumption went from 158.2 MB to 157.8 MB.
Total: 3.648700 ms (FindLiveObjects: 0.312500 ms CreateObjectMapping: 0.202700 ms MarkObjects: 2.933300 ms  DeleteObjects: 0.198800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 5.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 112 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (445.8 KB). Loaded Objects now: 4678.
Memory consumption went from 78.0 MB to 77.6 MB.
Total: 5.640900 ms (FindLiveObjects: 0.564100 ms CreateObjectMapping: 0.420100 ms MarkObjects: 4.407800 ms  DeleteObjects: 0.247200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002015 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[ScriptCompilation] NOT recompiling all scripts because this is an import worker process. Reason to compile was: Define symbols changed
[ScriptCompilation] NOT recompiling all scripts because this is an import worker process. Reason to compile was: Define symbols changed
Mono: successfully reloaded assembly
- Completed reload, in  1.180 seconds
Domain Reload Profiling:
	ReloadAssembly (1180ms)
		BeginReloadAssembly (112ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (30ms)
		EndReloadAssembly (1014ms)
			LoadAssemblies (72ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (201ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (40ms)
			SetupLoadedEditorAssemblies (681ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (52ms)
				ProcessInitializeOnLoadAttributes (553ms)
				ProcessInitializeOnLoadMethodAttributes (49ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4153 Unused Serialized files (Serialized files now loaded: 0)
Unloading 22 unused Assets / (445.5 KB). Loaded Objects now: 4681.
Memory consumption went from 159.4 MB to 159.0 MB.
Total: 91.911600 ms (FindLiveObjects: 0.295100 ms CreateObjectMapping: 0.187600 ms MarkObjects: 91.230900 ms  DeleteObjects: 0.196200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002975 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
[ScriptCompilation] NOT recompiling all scripts because this is an import worker process. Reason to compile was: Define symbols changed
[ScriptCompilation] NOT recompiling all scripts because this is an import worker process. Reason to compile was: Define symbols changed
Mono: successfully reloaded assembly
- Completed reload, in  1.160 seconds
Domain Reload Profiling:
	ReloadAssembly (1160ms)
		BeginReloadAssembly (116ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (981ms)
			LoadAssemblies (76ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (217ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (48ms)
			SetupLoadedEditorAssemblies (629ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (45ms)
				ProcessInitializeOnLoadAttributes (519ms)
				ProcessInitializeOnLoadMethodAttributes (43ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4153 Unused Serialized files (Serialized files now loaded: 0)
Unloading 22 unused Assets / (445.7 KB). Loaded Objects now: 4684.
Memory consumption went from 159.5 MB to 159.0 MB.
Total: 5.147400 ms (FindLiveObjects: 0.354400 ms CreateObjectMapping: 0.255100 ms MarkObjects: 4.304400 ms  DeleteObjects: 0.231600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.003050 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.236 seconds
Domain Reload Profiling:
	ReloadAssembly (1237ms)
		BeginReloadAssembly (122ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (1048ms)
			LoadAssemblies (84ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (235ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (37ms)
			SetupLoadedEditorAssemblies (674ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (20ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (51ms)
				ProcessInitializeOnLoadAttributes (546ms)
				ProcessInitializeOnLoadMethodAttributes (54ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4153 Unused Serialized files (Serialized files now loaded: 0)
Unloading 22 unused Assets / (445.6 KB). Loaded Objects now: 4687.
Memory consumption went from 159.5 MB to 159.0 MB.
Total: 3.605900 ms (FindLiveObjects: 0.336900 ms CreateObjectMapping: 0.332000 ms MarkObjects: 2.716700 ms  DeleteObjects: 0.218100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001945 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.211 seconds
Domain Reload Profiling:
	ReloadAssembly (1212ms)
		BeginReloadAssembly (118ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (38ms)
		EndReloadAssembly (1017ms)
			LoadAssemblies (84ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (239ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (37ms)
			SetupLoadedEditorAssemblies (638ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (20ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (49ms)
				ProcessInitializeOnLoadAttributes (522ms)
				ProcessInitializeOnLoadMethodAttributes (45ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4153 Unused Serialized files (Serialized files now loaded: 0)
Unloading 22 unused Assets / (445.6 KB). Loaded Objects now: 4690.
Memory consumption went from 159.5 MB to 159.0 MB.
Total: 3.450100 ms (FindLiveObjects: 0.303700 ms CreateObjectMapping: 0.229900 ms MarkObjects: 2.739600 ms  DeleteObjects: 0.175700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
