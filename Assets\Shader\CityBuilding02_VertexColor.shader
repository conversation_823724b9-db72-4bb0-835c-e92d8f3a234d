Shader "Custom/CityBuilding02_VertexColor"
{
    Properties
    {
        _Cutout("Cutout",float) = 0
        _Diffuse ("贴图", 2D) = "white" {}
        _Color ("颜色", Color) = (1,1,1,1)

        // RGB三通道顶点色预览相关属性（隐藏，由脚本设置）
        // R通道 (0-8)
        [HideInInspector] _ColorR0 ("R通道 0.9", Color) = (1,0,0,1)
        [HideInInspector] _ColorR1 ("R通道 0.7222", Color) = (0.9,0,0,1)
        [HideInInspector] _ColorR2 ("R通道 0.6333", Color) = (0.8,0,0,1)
        [HideInInspector] _ColorR3 ("R通道 0.5444", Color) = (0.7,0,0,1)
        [HideInInspector] _ColorR4 ("R通道 0.4556", Color) = (0.6,0,0,1)
        [HideInInspector] _ColorR5 ("R通道 0.3667", Color) = (0.5,0,0,1)
        [HideInInspector] _ColorR6 ("R通道 0.2778", Color) = (0.4,0,0,1)
        [HideInInspector] _ColorR7 ("R通道 0.1889", Color) = (0.3,0,0,1)
        [HideInInspector] _ColorR8 ("R通道 0.1", Color) = (0.2,0,0,1)

        // G通道 (9-17)
        [HideInInspector] _ColorG0 ("G通道 0.9", Color) = (0,1,0,1)
        [HideInInspector] _ColorG1 ("G通道 0.7222", Color) = (0,0.9,0,1)
        [HideInInspector] _ColorG2 ("G通道 0.6333", Color) = (0,0.8,0,1)
        [HideInInspector] _ColorG3 ("G通道 0.5444", Color) = (0,0.7,0,1)
        [HideInInspector] _ColorG4 ("G通道 0.4556", Color) = (0,0.6,0,1)
        [HideInInspector] _ColorG5 ("G通道 0.3667", Color) = (0,0.5,0,1)
        [HideInInspector] _ColorG6 ("G通道 0.2778", Color) = (0,0.4,0,1)
        [HideInInspector] _ColorG7 ("G通道 0.1889", Color) = (0,0.3,0,1)
        [HideInInspector] _ColorG8 ("G通道 0.1", Color) = (0,0.2,0,1)

        // B通道 (18-26)
        [HideInInspector] _ColorB0 ("B通道 0.9", Color) = (0,0,1,1)
        [HideInInspector] _ColorB1 ("B通道 0.7222", Color) = (0,0,0.9,1)
        [HideInInspector] _ColorB2 ("B通道 0.6333", Color) = (0,0,0.8,1)
        [HideInInspector] _ColorB3 ("B通道 0.5444", Color) = (0,0,0.7,1)
        [HideInInspector] _ColorB4 ("B通道 0.4556", Color) = (0,0,0.6,1)
        [HideInInspector] _ColorB5 ("B通道 0.3667", Color) = (0,0,0.5,1)
        [HideInInspector] _ColorB6 ("B通道 0.2778", Color) = (0,0,0.4,1)
        [HideInInspector] _ColorB7 ("B通道 0.1889", Color) = (0,0,0.3,1)
        [HideInInspector] _ColorB8 ("B通道 0.1", Color) = (0,0,0.2,1)
    }
    SubShader
    {
        Tags
        {
            "RenderType"="Opaque" "LightMode"="ForwardBase"
        }
        LOD 100

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_fwdbase

            #include "UnityCG.cginc"
            #include "Lighting.cginc"
            #include "AutoLight.cginc"


            float3 ApplyLut2D(sampler2D tex, float3 uvw, float3 scaleOffset)
            {
                // Strip format where `height = sqrt(width)`
                uvw.z *= scaleOffset.z;
                half shift = floor(uvw.z);
                uvw.xy = uvw.xy * scaleOffset.z * scaleOffset.xy + scaleOffset.xy * 0.5;
                uvw.x += shift * scaleOffset.y;
                uvw.xyz = lerp(
                    tex2Dlod(tex, float4(uvw.xy, 0, 0)).rgb,
                    tex2Dlod(tex, float4(uvw.xy + half2(scaleOffset.y, 0), 0, 0)).rgb,
                    uvw.z - shift
                );
                return uvw;
            }

            struct appdata
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
                float2 uv : TEXCOORD0;
                float4 color : COLOR;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 color : COLOR;
                float4 pos : SV_POSITION;
                float3 normal : NORMAL;
                float3 worldPos : TEXCOORD1;
                float3 normalDir : TEXCOORD2;
                LIGHTING_COORDS(2, 3)
            };

            sampler2D _Diffuse;
            float4 _Diffuse_ST;
            float4 _Color;
            float4 _Color2;
            float4 _Color3;
            float4 _Color4;
            float4 _Color5;
            float4 _Color6;
            float4 _Color7;
            float4 _Color8;
            float4 _Color9;
            float4 _Color10;
            float _AmbientIntensity;
            sampler2D _LutTex;
            float _LutRatio;
            float _UnlitSwitch;
            float _Cutout;

            v2f vert(appdata v)
            {
                v2f o;
                o.pos = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _Diffuse);
                o.normal = UnityObjectToWorldNormal(v.normal);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                o.normalDir = UnityObjectToWorldNormal(v.normal);
                o.color = v.color;
                // 根据顶点色R通道值分配对应的分段颜色
                // 区间分别为：0.9附近、0.8附近、0.7附近、0.6附近、0.5附近、0.4附近、0.3附近、0.2附近、0.1附近
                o.color = _Color2 * step(0.89, o.color.r)*(1-step(0.91, o.color.r)) +  // 0.9附近
                    _Color3 * step(0.7122, o.color.r)*(1-step(0.7322, o.color.r)) +        // 0.8附近
                    _Color4 * step(0.6233, o.color.r)*(1-step(0.6433, o.color.r)) +        // 0.7附近
                    _Color5 * step(0.5344, o.color.r)*(1-step(0.5544, o.color.r)) +        // 0.6附近
                    _Color6 * step(0.4456, o.color.r)*(1-step(0.4656, o.color.r)) +        // 0.5附近
                    _Color7 * step(0.3567, o.color.r)*(1-step(0.3767, o.color.r)) +        // 0.4附近
                    _Color8 * step(0.2678, o.color.r)*(1-step(0.2878, o.color.r)) +        // 0.3附近
                    _Color9 * step(0.1789, o.color.r)*(1-step(0.1989, o.color.r)) +        // 0.2附近
                    _Color10 * step(0.09, o.color.r)*(1-step(0.11, o.color.r)) +       // 0.1附近
                    0 * (1-step(0.09, o.color.r));                    // 其他区间保持黑色
                TRANSFER_VERTEX_TO_FRAGMENT(o);
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                // 采样贴图
                fixed4 albedo = tex2D(_Diffuse, i.uv) * _Color * i.color;


                float3 normalDirection = normalize(i.normalDir);

                // 漫反射
                float3 normal = normalize(i.normal);
                float3 lightDir = normalize(_WorldSpaceLightPos0.xyz);
                float NdotL = max(0, dot(normal, lightDir));
                float3 diffuse = _LightColor0.rgb * 1;
                // 光照衰减
                float attenuation = LIGHT_ATTENUATION(i) * NdotL;

                //整体校色
                diffuse = lerp(diffuse, diffuse * UNITY_LIGHTMODEL_AMBIENT * 2, normalDirection.y);

                diffuse = lerp(diffuse * unity_AmbientEquator, diffuse, NdotL);

                diffuse = lerp(diffuse * unity_AmbientGround, diffuse, attenuation);

                // 最终颜色 = 环境光 + 漫反射
                float3 finalColor = diffuse * albedo.rgb;
                finalColor = min(1, finalColor);


                float3 userLutParams = float3(1.0 / 256, 1.0 / 16, 15);
                float3 outLut = ApplyLut2D(_LutTex, finalColor.rgb, userLutParams);
                finalColor.rgb = lerp(finalColor.rgb, outLut, _LutRatio);

                finalColor.rgb = lerp(finalColor.rgb, albedo.rgb, _UnlitSwitch);

                clip(albedo.a - _Cutout);
                return float4(finalColor, albedo.a);
            }
            ENDCG
        }

        // 阴影投射Pass
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_shadowcaster
            #include "UnityCG.cginc"

            struct v2f
            {
                V2F_SHADOW_CASTER;
            };

            v2f vert(appdata_base v)
            {
                v2f o;
                TRANSFER_SHADOW_CASTER_NORMALOFFSET(o)
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                    SHADOW_CASTER_FRAGMENT(i)
            }
            ENDCG
        }
    }
    //    FallBack "Diffuse"
}