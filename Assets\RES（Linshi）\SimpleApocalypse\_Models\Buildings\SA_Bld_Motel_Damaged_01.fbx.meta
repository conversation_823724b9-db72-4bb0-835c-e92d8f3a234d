fileFormatVersion: 2
guid: d79cb0350f78f2c4ca9d2bd41118bf2a
timeCreated: **********
licenseType: Store
ModelImporter:
  serializedVersion: 18
  fileIDToRecycleName:
    100000: //RootNode
    100002: SA_Bld_Motel_Damaged_Extras_01
    100004: SA_Bld_Motel_Damaged_Growth_01
    400000: //RootNode
    400002: SA_Bld_Motel_Damaged_Extras_01
    400004: SA_Bld_Motel_Damaged_Growth_01
    2300000: //RootNode
    2300002: SA_Bld_Motel_Damaged_Extras_01
    2300004: SA_Bld_Motel_Damaged_Growth_01
    3300000: //RootNode
    3300002: SA_Bld_Motel_Damaged_Extras_01
    3300004: SA_Bld_Motel_Damaged_Growth_01
    4300000: SA_Bld_Motel_Damaged_01
    4300002: SA_Bld_Motel_Damaged_Growth_01
    4300004: SA_Bld_Motel_Damaged_Extras_01
    9500000: //RootNode
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    optimizeGameObjects: 0
    motionNodeName: 
    animationCompression: 1
    animationRotationError: .5
    animationPositionError: .5
    animationScaleError: .5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 100
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 10
    splitTangentsAcrossUV: 1
    normalImportMode: 1
    tangentImportMode: 1
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: .5
    foreArmTwist: .5
    upperLegTwist: .5
    legTwist: .5
    armStretch: .0500000007
    legStretch: .0500000007
    feetSpacing: 0
    rootMotionBoneName: 
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
